# RunSim GUI 仪表板用户手册

## 概述

RunSim GUI 仪表板是一个基于Web的项目管理工具，为RunSim GUI提供了丰富的数据可视化和管理功能。

### 主要功能

- **项目仪表板**: 实时显示项目进度、用例统计和BUG趋势
- **用例管理**: Excel文件导入导出、用例状态跟踪、批量操作
- **BUG管理**: BUG记录、统计分析、趋势图表
- **数据导出**: 支持JSON、Excel、PDF格式的数据导出

## 系统要求

### 软件依赖
- Python 3.8+
- Flask 2.0+
- openpyxl (用于Excel处理)
- 现代Web浏览器 (Chrome, Firefox, Edge, Safari)

### 硬件要求
- 内存: 最少512MB，推荐1GB+
- 存储: 最少100MB可用空间
- 网络: 本地网络连接

## 安装和启动

### 1. 通过RunSim GUI启动

1. 启动RunSim GUI主程序
2. 在菜单栏中选择 "工具" → "项目仪表板"
3. 系统会自动启动Web服务器并打开浏览器

### 2. 独立启动

```bash
cd plugins/builtin/dashboard_web
python app.py
```

然后在浏览器中访问: `http://127.0.0.1:5000`

## 功能详解

### 仪表板主页

仪表板主页提供项目的整体概览：

#### 统计卡片
- **总用例数**: 显示项目中的用例总数
- **通过用例**: 显示状态为"Pass"的用例数量
- **失败用例**: 显示状态为"Fail"的用例数量
- **进行中**: 显示状态为"On-Going"的用例数量

#### 图表展示
- **项目进度图**: 饼图显示各阶段的完成情况
- **用例状态分布**: 饼图显示用例状态分布
- **BUG趋势图**: 折线图显示BUG的新增和修复趋势

### 用例管理

#### Excel文件导入

1. 点击"导入Excel"按钮
2. 选择或拖拽Excel文件到上传区域
3. 选择工作表（如果有多个）
4. 选择项目（可选）
5. 点击"开始导入"

**支持的Excel格式**:
- 文件格式: .xlsx, .xls
- 必需列: 用例名称 (case_name)
- 可选列: 类别、编号、测试范围、功能点等

#### 用例查看和编辑

- **搜索**: 在搜索框中输入关键词快速查找用例
- **筛选**: 使用状态筛选器过滤特定状态的用例
- **编辑**: 点击编辑按钮修改用例状态
- **删除**: 点击删除按钮移除用例

#### 数据导出

1. 设置筛选条件（可选）
2. 点击"导出Excel"按钮
3. 文件会自动下载到浏览器默认下载目录

#### 模板下载

点击"下载模板"按钮获取标准格式的Excel模板，按照模板格式准备数据可以提高导入成功率。

### BUG管理

#### 创建BUG

1. 点击"新建BUG"按钮
2. 填写BUG信息：
   - BUG ID（必填）
   - BUG类型
   - 提交系统
   - 验证阶段
   - 描述
   - 发现平台
   - 发现用例
   - 严重程度
   - 状态
   - 提交人
   - 验证人

3. 点击"保存"提交

#### BUG查看和管理

- **列表视图**: 表格形式显示所有BUG
- **搜索**: 支持按BUG ID、描述等字段搜索
- **筛选**: 按状态、严重程度、类型等筛选
- **编辑**: 点击编辑按钮修改BUG信息
- **删除**: 点击删除按钮移除BUG记录

#### BUG统计

- **状态分布**: 显示不同状态BUG的数量分布
- **严重程度分布**: 显示不同严重程度BUG的分布
- **趋势分析**: 显示BUG新增和修复的时间趋势

## 高级功能

### 数据导出

#### JSON导出
- 包含完整的项目数据
- 适合数据分析和备份
- 访问: `/api/dashboard/export/json`

#### Excel导出
- 包含用例和BUG数据
- 支持图表和原始数据
- 适合报告生成

#### PDF导出
- 生成项目报告
- 包含图表和统计信息
- 适合正式文档

### API接口

仪表板提供RESTful API接口，支持程序化访问：

#### 仪表板API
- `GET /api/dashboard/statistics` - 获取统计数据
- `GET /api/dashboard/progress` - 获取项目进度
- `GET /api/dashboard/case_status` - 获取用例状态分布
- `GET /api/dashboard/bug_trend` - 获取BUG趋势

#### 用例管理API
- `GET /api/testplan/cases` - 获取用例列表
- `POST /api/testplan/import` - 导入Excel文件
- `GET /api/testplan/export` - 导出Excel文件
- `PUT /api/testplan/case/{id}` - 更新用例状态

#### BUG管理API
- `GET /api/bugs` - 获取BUG列表
- `POST /api/bugs` - 创建新BUG
- `PUT /api/bugs/{id}` - 更新BUG
- `DELETE /api/bugs/{id}` - 删除BUG

## 故障排除

### 常见问题

#### 1. 页面无法访问
**症状**: 浏览器显示"无法访问此网站"

**解决方案**:
1. 检查Web服务器是否启动
2. 确认端口5000未被其他程序占用
3. 检查防火墙设置
4. 尝试重启RunSim GUI

#### 2. Excel导入失败
**症状**: 上传Excel文件后显示导入错误

**解决方案**:
1. 检查Excel文件格式是否正确
2. 确保包含必需的"用例名称"列
3. 检查文件大小是否超过16MB限制
4. 尝试使用模板文件格式

#### 3. 数据显示异常
**症状**: 图表或统计数据显示不正确

**解决方案**:
1. 刷新浏览器页面
2. 清除浏览器缓存
3. 检查数据库连接
4. 重启Web服务器

#### 4. 性能问题
**症状**: 页面加载缓慢或响应延迟

**解决方案**:
1. 运行性能优化脚本: `python performance_optimizer.py`
2. 清理数据库: 删除不需要的历史数据
3. 重启Web服务器
4. 检查系统资源使用情况

### 日志查看

#### Web服务器日志
- 位置: RunSim GUI控制台输出
- 内容: 请求日志、错误信息、性能警告

#### 数据库日志
- 位置: `data/dashboard.db-wal` (WAL模式)
- 内容: 数据库操作记录

### 性能优化

#### 自动优化
运行性能优化脚本：
```bash
python performance_optimizer.py
```

#### 手动优化
1. **数据库优化**:
   - 定期执行VACUUM清理
   - 重建索引
   - 清理历史数据

2. **内存优化**:
   - 重启Web服务器
   - 清理浏览器缓存
   - 关闭不必要的浏览器标签

3. **网络优化**:
   - 使用本地网络
   - 避免VPN连接
   - 关闭其他网络密集型应用

## 技术支持

### 联系方式
- 技术文档: 查看项目README文件
- 问题报告: 通过RunSim GUI反馈功能
- 开发团队: 联系RunSim开发团队

### 版本信息
- 当前版本: 1.0.0
- 发布日期: 2024年12月
- 兼容性: RunSim GUI 最新版本

### 更新日志
- v1.0.0: 初始版本发布
  - 基础仪表板功能
  - 用例管理功能
  - BUG管理功能
  - 数据导出功能
