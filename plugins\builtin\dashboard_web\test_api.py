#!/usr/bin/env python3
"""
测试用例创建API
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.testplan import TestCaseManager

def test_case_creation():
    """测试用例创建功能"""
    app = create_app()
    with app.app_context():
        print("Testing case_name_exists method...")

        # 测试用例名称是否存在
        result = TestCaseManager.case_name_exists('test_case')
        print(f"case_name_exists result: {result}")

        print("\nTesting create_test_case method...")

        try:
            # 创建测试用例
            case_id = TestCaseManager.create_test_case(
                category='TEST',
                case_name='test_case_001',
                test_areas='测试区域',
                test_scope='测试范围',
                function_point='测试功能点描述',
                check_point='测试检查点描述',
                cover='测试覆盖内容',
                owner='测试人员'
            )
            print(f"Created case with ID: {case_id}")

            # 再次检查用例名称是否存在
            result = TestCaseManager.case_name_exists('test_case_001')
            print(f"case_name_exists after creation: {result}")

            # 测试获取用例列表
            cases, total = TestCaseManager.get_test_cases(page=1, page_size=10)
            print(f"Total cases: {total}")
            if cases:
                print(f"First case: {cases[0]['case_name']}")

        except Exception as e:
            print(f"Error creating case: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_case_creation()
