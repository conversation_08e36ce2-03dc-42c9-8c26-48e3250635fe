import openpyxl

try:
    wb = openpyxl.load_workbook("TestPlan_Template.xlsx")
    ws = wb["TP"]
    
    print("✅ 文件打开成功")
    print(f"工作表: {wb.sheetnames}")
    print(f"行数: {ws.max_row}, 列数: {ws.max_column}")
    
    # 检查关键表头
    headers = []
    for col in range(1, 22):
        val = ws.cell(row=3, column=col).value
        if val:
            headers.append(f"{chr(64+col)}: {val}")
    
    print("表头:")
    for h in headers:
        print(f"  {h}")
    
    wb.close()
    print("✅ 检查完成")
    
except Exception as e:
    print(f"❌ 错误: {e}")
