{"test_time": "2025-06-03T10:07:31.106832", "results": {"post_phase_parsing": true, "api_endpoints": {"/api/dashboard/statistics": "❌ 连接失败: HTTPConnectionPool(host='127.0.0.1', port=5001): Max retries exceeded with url: /api/dashboard/statistics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000294C0B67370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "/api/dashboard/progress": "❌ 连接失败: HTTPConnectionPool(host='127.0.0.1', port=5001): Max retries exceeded with url: /api/dashboard/progress (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000294C0B67C70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "/api/dashboard/phase_distribution": "❌ 连接失败: HTTPConnectionPool(host='127.0.0.1', port=5001): Max retries exceeded with url: /api/dashboard/phase_distribution (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000294C0BA0550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "/api/dashboard/phase_progress": "❌ 连接失败: HTTPConnectionPool(host='127.0.0.1', port=5001): Max retries exceeded with url: /api/dashboard/phase_progress (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000294C0BA0DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "/api/dashboard/bug_trend": "❌ 连接失败: HTTPConnectionPool(host='127.0.0.1', port=5001): Max retries exceeded with url: /api/dashboard/bug_trend (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000294C0BA1630>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "phase_analyzer": false, "excel_parser_compatibility": true}, "summary": {"total_tests": 4, "passed": 2, "failed": 1}}