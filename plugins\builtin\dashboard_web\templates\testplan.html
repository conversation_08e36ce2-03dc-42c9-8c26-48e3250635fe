{% extends "base.html" %}

{% block title %}用例管理 - RunSim 项目仪表板{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .upload-area:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .upload-area.dragover {
        border-color: #007bff;
        background-color: #e3f2fd;
    }
    .case-table {
        font-size: 0.9rem;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .status-pass { background-color: #d4edda; color: #155724; }
    .status-pending { background-color: #e2e3e5; color: #383d41; }
    .status-ongoing { background-color: #fff3cd; color: #856404; }
    .status-na { background-color: #f8f9fa; color: #6c757d; }
    /* 兼容旧状态样式 */
    .status-fail { background-color: #f8d7da; color: #721c24; }
    .status-not-started { background-color: #e2e3e5; color: #383d41; }
    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    .progress-mini {
        height: 4px;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">用例管理</h1>
                <p class="text-muted mb-0">管理测试用例和TestPlan文件</p>
            </div>
            <div>
                <button class="btn btn-outline-secondary me-2" onclick="downloadTemplate()">
                    <i class="fas fa-download me-1"></i>下载模板
                </button>
                <button class="btn btn-success me-2" onclick="exportCases()">
                    <i class="fas fa-file-excel me-1"></i>导出Excel
                </button>
                <button class="btn btn-success me-2" onclick="createCase()">
                    <i class="fas fa-plus me-1"></i>添加用例
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="fas fa-upload me-1"></i>导入Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-white-50">总用例数</h6>
                        <h3 class="mb-0" id="total-cases">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list-check fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-white-50">已通过</h6>
                        <h3 class="mb-0" id="passed-cases">-</h3>
                        <div class="progress progress-mini">
                            <div class="progress-bar bg-light" id="pass-progress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-white-50">待处理</h6>
                        <h3 class="mb-0" id="pending-cases">-</h3>
                        <div class="progress progress-mini">
                            <div class="progress-bar bg-light" id="pending-progress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hourglass-half fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-white-50">进行中</h6>
                        <h3 class="mb-0" id="ongoing-cases">-</h3>
                        <div class="progress progress-mini">
                            <div class="progress-bar bg-light" id="ongoing-progress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="search-input" placeholder="搜索用例名称、类别、功能点...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="status-filter">
                <option value="">所有状态</option>
                <option value="PASS">通过</option>
                <option value="Pending">未开始调试</option>
                <option value="On-Going">正在调试</option>
                <option value="N/A">不适用</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="stage-filter">
                <option value="">所有阶段</option>
                <option value="subsys">子系统级</option>
                <option value="top">TOP级</option>
                <option value="post_subsys">后仿子系统</option>
                <option value="post_top">后仿TOP</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="page-size">
                <option value="25">25条/页</option>
                <option value="50" selected>50条/页</option>
                <option value="100">100条/页</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-primary w-100" onclick="refreshCases()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 用例表格 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>用例列表
        </h5>
        <div>
            <span class="badge bg-secondary" id="case-count">0 条用例</span>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover case-table mb-0">
                <thead class="table-light">
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th style="width: 180px;">用例名称</th>
                        <th style="width: 100px;">类别</th>
                        <th style="width: 120px;">测试区域</th>
                        <th style="width: 120px;">测试范围</th>
                        <th style="width: 100px;">覆盖</th>
                        <th style="width: 100px;">子系统级</th>
                        <th style="width: 100px;">TOP级</th>
                        <th style="width: 100px;">后仿子系统</th>
                        <th style="width: 100px;">后仿TOP</th>
                        <th style="width: 120px;">创建时间</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="cases-table-body">
                    <tr>
                        <td colspan="12" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载用例数据...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <nav aria-label="用例分页">
            <ul class="pagination pagination-sm mb-0" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 导入Excel模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>导入Excel文件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="import-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">选择Excel文件</label>
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">支持 .xlsx 和 .xls 格式</p>
                            <input type="file" id="file-input" name="file" accept=".xlsx,.xls" style="display: none;">
                        </div>
                        <div id="file-info" class="mt-2" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-file-excel me-2"></i>
                                <span id="file-name"></span>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">工作表名称（可选）</label>
                            <input type="text" class="form-control" name="sheet_name" placeholder="留空使用第一个工作表">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">项目（可选）</label>
                            <select class="form-select" name="project_id">
                                <option value="">默认项目</option>
                                <!-- 项目选项将通过JavaScript加载 -->
                            </select>
                        </div>
                    </div>
                </form>

                <div id="import-progress" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                    </div>
                    <p class="text-center">正在导入，请稍候...</p>
                </div>

                <div id="import-result" style="display: none;">
                    <!-- 导入结果将通过JavaScript显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="import-btn" onclick="importExcel()">
                    <i class="fas fa-upload me-1"></i>开始导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 状态更新模态框 -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>更新用例状态
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="status-form">
                    <input type="hidden" id="status-case-id">
                    <div class="mb-3">
                        <label class="form-label">用例名称</label>
                        <input type="text" class="form-control" id="status-case-name" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">阶段类型</label>
                        <select class="form-select" id="status-stage-type" required>
                            <option value="subsys">子系统级</option>
                            <option value="top">TOP级</option>
                            <option value="post_subsys">后仿子系统</option>
                            <option value="post_top">后仿TOP</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新状态</label>
                        <select class="form-select" id="status-new-status" required>
                            <option value="Pending">未开始调试</option>
                            <option value="On-Going">正在调试</option>
                            <option value="PASS">通过</option>
                            <option value="N/A">不适用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateCaseStatus()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 创建用例模态框 -->
<div class="modal fade" id="createCaseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>添加新用例
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-case-form" class="needs-validation" novalidate>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">用例类别 <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="输入测试用例的分类，如MEM表示内存相关，BUS表示总线相关等"></i></label>
                            <input type="text" class="form-control" id="case-category" required 
                                   pattern="[A-Z]{2,5}"
                                   placeholder="例如: MEM, BUS, REG等">
                            <div class="invalid-feedback">请输入2-5个大写字母的类别代码</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">用例名称 <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="用例名称应遵循格式：类别_模块_功能_序号"></i></label>
                            <input type="text" class="form-control" id="case-name" required
                                   pattern="[A-Z]+_[A-Z0-9]+_[A-Z0-9]+_[0-9]{3}"
                                   placeholder="例如: APC_MEM_BIST_001">
                            <div class="invalid-feedback">请按格式输入用例名称（例如：APC_MEM_BIST_001）</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">测试区域 <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="指定要测试的具体模块或区域"></i></label>
                            <input type="text" class="form-control" id="test-areas" required
                                   minlength="2"
                                   placeholder="测试模块/区域">
                            <div class="invalid-feedback">请输入测试区域，至少2个字符</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">测试范围 <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="描述测试的具体范围和边界"></i></label>
                            <input type="text" class="form-control" id="test-scope" required
                                   minlength="5"
                                   placeholder="具体测试范围">
                            <div class="invalid-feedback">请详细描述测试范围，至少5个字符</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">功能点 <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="描述测试用例要验证的具体功能"></i></label>
                            <textarea class="form-control" id="function-point" rows="2" required
                                   minlength="10"
                                   placeholder="描述测试的功能点..."></textarea>
                            <div class="invalid-feedback">请详细描述功能点，至少10个字符</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">检查点 <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="列出测试中需要验证的具体检查项"></i></label>
                            <textarea class="form-control" id="check-point" rows="2" required
                                   minlength="10"
                                   placeholder="描述检查的内容..."></textarea>
                            <div class="invalid-feedback">请详细描述检查点，至少10个字符</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">覆盖内容</label>
                            <input type="text" class="form-control" id="cover" required
                                   placeholder="覆盖的场景/内容">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">负责人</label>
                            <input type="text" class="form-control" id="owner" required
                                   placeholder="用例负责人">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">开始时间</label>
                            <input type="date" class="form-control" id="start-time">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">结束时间</label>
                            <input type="date" class="form-control" id="end-time">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" id="remarks" rows="2" 
                                   placeholder="可选：添加备注说明"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitNewCase()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentPage = 1;
let currentPageSize = 50;
let currentSearch = '';
let currentStatusFilter = '';
let currentStageFilter = '';

$(document).ready(function() {
    // 初始化页面
    initializePage();

    // 绑定事件
    bindEvents();

    // 加载数据
    loadStatistics();
    loadTestCases();
});

function initializePage() {
    // 设置文件上传区域
    setupFileUpload();

    // 设置搜索防抖
    setupSearchDebounce();
}

function bindEvents() {
    // 筛选器变化事件
    $('#status-filter, #stage-filter, #page-size').on('change', function() {
        currentPage = 1;
        currentPageSize = parseInt($('#page-size').val());
        currentStatusFilter = $('#status-filter').val();
        currentStageFilter = $('#stage-filter').val();
        loadTestCases();
    });
}

function setupFileUpload() {
    const uploadArea = $('#upload-area');
    const fileInput = $('#file-input');

    // 点击上传区域触发文件选择
    uploadArea.on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('点击上传区域，触发文件选择');
        fileInput.trigger('click');
    });

    // 确保文件输入框也能响应点击
    fileInput.on('click', function(e) {
        e.stopPropagation();
        console.log('文件输入框被点击');
    });

    // 文件选择变化事件
    fileInput.on('change', function(e) {
        console.log('文件选择变化事件触发');
        const file = this.files[0];
        if (file) {
            console.log('选择的文件:', file.name);
            showFileInfo(file);
        } else {
            console.log('没有选择文件');
        }
    });

    // 拖拽上传
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('dragover');
    });

    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
    });

    uploadArea.on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            console.log('拖拽文件:', files[0].name);
            fileInput[0].files = files;
            showFileInfo(files[0]);
        }
    });

    // 添加调试信息
    console.log('文件上传功能初始化完成');
    console.log('上传区域元素:', uploadArea.length);
    console.log('文件输入框元素:', fileInput.length);
}

function setupSearchDebounce() {
    let searchTimeout;
    $('#search-input').on('input', function() {
        clearTimeout(searchTimeout);
        const searchValue = $(this).val().trim();

        searchTimeout = setTimeout(function() {
            currentSearch = searchValue;
            currentPage = 1;
            loadTestCases();
        }, 500);
    });
}

function showFileInfo(file) {
    $('#file-name').text(file.name);
    $('#file-info').show();
    $('#import-btn').prop('disabled', false);
}

function clearFile() {
    $('#file-input').val('');
    $('#file-info').hide();
    $('#import-btn').prop('disabled', true);
}

function loadStatistics() {
    // 使用用例列表API来获取准确的统计数据
    $.get('/api/testplan/cases', {page: 1, page_size: 1})
        .done(function(response) {
            if (response.success) {
                // 获取总数
                const totalCases = response.data.pagination.total_count;

                // 如果有用例，获取详细统计
                if (totalCases > 0) {
                    $.get('/api/testplan/statistics')
                        .done(function(statsResponse) {
                            if (statsResponse.success) {
                                updateStatistics(statsResponse.data);
                            }
                        })
                        .fail(function() {
                            // 如果统计API失败，使用基础数据
                            updateStatistics({total_cases: totalCases});
                        });
                } else {
                    // 没有用例时显示0
                    updateStatistics({total_cases: 0});
                }
            }
        })
        .fail(function() {
            console.error('加载统计数据失败');
        });
}

function updateStatistics(stats) {
    // 支持两种数据格式：新格式(stats.cases)和旧格式(stats.total_*)
    const cases = stats.cases || stats;
    const totalCases = cases.total || stats.total_cases || 0;
    const passCount = cases.passed || stats.total_passed || 0;
    const pendingCount = cases.pending || stats.total_pending || 0;
    const ongoingCount = cases.running || stats.total_ongoing || 0;

    $('#total-cases').text(totalCases);
    $('#passed-cases').text(passCount);
    $('#pending-cases').text(pendingCount);
    $('#ongoing-cases').text(ongoingCount);

    // 更新进度条
    const total = totalCases || 1;
    $('#pass-progress').css('width', (passCount / total * 100) + '%');
    $('#pending-progress').css('width', (pendingCount / total * 100) + '%');
    $('#ongoing-progress').css('width', (ongoingCount / total * 100) + '%');

    // 更新分级别统计（如果有的话）
    if (cases.subsys && $('#subsys-passed').length) {
        $('#subsys-passed').text(cases.subsys.passed || 0);
        $('#subsys-pending').text(cases.subsys.pending || 0);
        $('#subsys-running').text(cases.subsys.running || 0);
    }

    if (cases.top && $('#top-passed').length) {
        $('#top-passed').text(cases.top.passed || 0);
        $('#top-pending').text(cases.top.pending || 0);
        $('#top-running').text(cases.top.running || 0);
    }
}

function loadTestCases() {
    const params = {
        page: currentPage,
        page_size: currentPageSize
    };

    if (currentSearch) params.search = currentSearch;
    if (currentStatusFilter) params.status = currentStatusFilter;

    $.get('/api/testplan/cases', params)
        .done(function(response) {
            if (response.success) {
                displayTestCases(response.data.cases);
                updatePagination(response.data.pagination);
                $('#case-count').text(response.data.pagination.total_count + ' 条用例');
            } else {
                showError('加载用例失败: ' + response.error);
            }
        })
        .fail(function() {
            showError('加载用例失败，请检查网络连接');
        });
}

function displayTestCases(cases) {
    const tbody = $('#cases-table-body');
    tbody.empty();

    if (cases.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="12" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无用例数据</div>
                </td>
            </tr>
        `);
        return;
    }

    cases.forEach((testCase, index) => {
        const rowNum = (currentPage - 1) * currentPageSize + index + 1;
        const row = createCaseRow(testCase, rowNum);
        tbody.append(row);
    });
}

function createCaseRow(testCase, rowNum) {
    const formatDate = (dateStr) => {
        if (!dateStr) return '-';
        try {
            return new Date(dateStr).toLocaleDateString('zh-CN');
        } catch {
            return '-';
        }
    };

    const createStatusBadge = (status) => {
        const statusClass = {
            'PASS': 'status-pass',
            'Pending': 'status-pending',
            'On-Going': 'status-ongoing',
            'N/A': 'status-na',
            // 兼容旧状态
            'Pass': 'status-pass',
            'Fail': 'status-fail',
            'Not Started': 'status-not-started'
        }[status] || 'status-pending';

        return `<span class="badge ${statusClass}">${status || 'Pending'}</span>`;
    };

    return `
        <tr>
            <td>${rowNum}</td>
            <td>
                <div class="fw-bold">${testCase.case_name || '-'}</div>
                <small class="text-muted">${testCase.function_point || ''}</small>
            </td>
            <td>${testCase.category || '-'}</td>
            <td>${testCase.test_areas || '-'}</td>
            <td>${testCase.test_scope || '-'}</td>
            <td>${testCase.cover || '-'}</td>
            <td>${createStatusBadge(testCase.subsys_status)}</td>
            <td>${createStatusBadge(testCase.top_status)}</td>
            <td>${createStatusBadge(testCase.post_subsys_status)}</td>
            <td>${createStatusBadge(testCase.post_top_status)}</td>
            <td>${formatDate(testCase.created_at)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-outline-primary btn-sm me-1"
                            onclick="editCaseStatus(${testCase.id}, '${testCase.case_name}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm"
                            onclick="deleteCase(${testCase.id}, '${testCase.case_name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

function updatePagination(pagination) {
    const paginationEl = $('#pagination');
    paginationEl.empty();

    if (pagination.total_pages <= 1) return;

    // 上一页
    if (pagination.has_prev) {
        paginationEl.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${pagination.page - 1})">上一页</a>
            </li>
        `);
    }

    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === pagination.page ? 'active' : '';
        paginationEl.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `);
    }

    // 下一页
    if (pagination.has_next) {
        paginationEl.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${pagination.page + 1})">下一页</a>
            </li>
        `);
    }
}

function changePage(page) {
    currentPage = page;
    loadTestCases();
}

function refreshCases() {
    loadStatistics();
    loadTestCases();
}

function downloadTemplate() {
    window.open('/api/testplan/template', '_blank');
}

function exportCases() {
    const params = new URLSearchParams();
    if (currentSearch) params.append('search', currentSearch);
    if (currentStatusFilter) params.append('status', currentStatusFilter);

    window.open('/api/testplan/export?' + params.toString(), '_blank');
}

function importExcel() {
    const formData = new FormData($('#import-form')[0]);

    if (!formData.get('file')) {
        showError('请选择要导入的Excel文件');
        return;
    }

    // 显示进度
    $('#import-progress').show();
    $('#import-btn').prop('disabled', true);

    $.ajax({
        url: '/api/testplan/import',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#import-progress').hide();
            showImportResult(response);

            if (response.success) {
                // 刷新数据
                setTimeout(() => {
                    loadStatistics();
                    loadTestCases();
                    $('#importModal').modal('hide');
                }, 2000);
            }
        },
        error: function() {
            $('#import-progress').hide();
            $('#import-btn').prop('disabled', false);
            showError('导入失败，请检查网络连接');
        }
    });
}

function showImportResult(response) {
    const resultDiv = $('#import-result');
    resultDiv.empty().show();

    if (response.success) {
        const data = response.data;

        // 构建成功信息
        let successHtml = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle me-2"></i>导入完成</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li>解析用例: ${data.total_parsed} 条</li>
                            <li>有效用例: ${data.valid_cases} 条</li>
                            <li>处理成功: ${data.processed_count} 条</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="mb-0">
                            <li><span class="text-primary">新增用例: ${data.inserted_count} 条</span></li>
                            <li><span class="text-info">更新用例: ${data.updated_count} 条</span></li>
                            <li><span class="text-danger">失败: ${data.failed_count} 条</span></li>
                        </ul>
                    </div>
                </div>
        `;

        // 如果有更新的用例，显示特别提示
        if (data.updated_count > 0) {
            successHtml += `
                <div class="mt-2 p-2 bg-light rounded">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        已更新 ${data.updated_count} 个现有用例的Phase和Status信息
                    </small>
                </div>
            `;
        }

        successHtml += '</div>';
        resultDiv.html(successHtml);

        // 显示导入错误（如果有）
        if (response.import_errors && response.import_errors.length > 0) {
            resultDiv.append(`
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>导入警告</h6>
                    <div style="max-height: 200px; overflow-y: auto;">
                        <ul class="mb-0">
                            ${response.import_errors.map(err => `<li>${err}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `);
        }

        // 显示验证错误（如果有）
        if (response.validation_errors && response.validation_errors.length > 0) {
            resultDiv.append(`
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>数据验证信息</h6>
                    <div style="max-height: 150px; overflow-y: auto;">
                        <ul class="mb-0">
                            ${response.validation_errors.map(err => `<li>${err}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `);
        }
    } else {
        resultDiv.html(`
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>导入失败</h6>
                <p class="mb-0">${response.error}</p>
                ${response.message ? `<small class="text-muted">${response.message}</small>` : ''}
            </div>
        `);
    }

    $('#import-btn').prop('disabled', false);
}

function editCaseStatus(caseId, caseName) {
    $('#status-case-id').val(caseId);
    $('#status-case-name').val(caseName);
    $('#statusModal').modal('show');
}

function updateCaseStatus() {
    const caseId = $('#status-case-id').val();
    const stageType = $('#status-stage-type').val();
    const newStatus = $('#status-new-status').val();

    if (!caseId || !stageType || !newStatus) {
        showError('请填写完整信息');
        return;
    }

    $.ajax({
        url: `/api/testplan/case/${caseId}`,
        type: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({
            stage_type: stageType,
            status: newStatus,
            changed_by: 'web_user'
        }),
        success: function(response) {
            if (response.success) {
                showSuccess('状态更新成功');
                $('#statusModal').modal('hide');
                loadStatistics();
                loadTestCases();
            } else {
                showError('状态更新失败: ' + response.error);
            }
        },
        error: function() {
            showError('状态更新失败，请检查网络连接');
        }
    });
}

function deleteCase(caseId, caseName) {
    if (!confirm(`确定要删除用例 "${caseName}" 吗？此操作不可撤销。`)) {
        return;
    }

    $.ajax({
        url: `/api/testplan/case/${caseId}`,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                showSuccess('用例删除成功');
                loadStatistics();
                loadTestCases();
            } else {
                showError('删除失败: ' + response.error);
            }
        },
        error: function() {
            showError('删除失败，请检查网络连接');
        }
    });
}

function createCase() {
    // 清空表单
    $('#create-case-form')[0].reset();
    // 设置默认日期
    const today = new Date().toISOString().split('T')[0];
    $('#start-time').val(today);
    // 显示模态框
    $('#createCaseModal').modal('show');
}

function submitNewCase() {
    const form = document.getElementById('create-case-form');
    
    // 初始化所有tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });

    // 验证表单
    if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
        form.classList.add('was-validated');
        return;
    }

    // 显示加载指示器
    const submitBtn = document.querySelector('#createCaseModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
    submitBtn.disabled = true;

    // 收集表单数据
    const caseData = {
        category: document.getElementById('case-category').value,
        case_name: document.getElementById('case-name').value,
        test_areas: document.getElementById('test-areas').value,
        test_scope: document.getElementById('test-scope').value,
        function_point: document.getElementById('function-point').value,
        check_point: document.getElementById('check-point').value,
        cover: document.getElementById('cover').value,
        owner: document.getElementById('owner').value,
        start_time: document.getElementById('start-time').value,
        end_time: document.getElementById('end-time').value,
        remarks: document.getElementById('remarks').value
    };

    // 发送 API 请求
    fetch('/api/cases', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(caseData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示成功消息
            showNotification('success', '用例创建成功！');
            
            // 刷新用例列表
            loadTestCases();
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('createCaseModal'));
            modal.hide();
        } else {
            showNotification('error', data.message || '创建用例失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('error', '创建用例时发生错误，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function showNotification(type, message) {
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
    const color = type === 'success' ? 'success' : 'danger';
    
    const toast = `
    <div class="toast align-items-center text-white bg-${color} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${icon} me-2"></i>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    </div>`;
    
    const toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.innerHTML = toast;
    document.body.appendChild(toastContainer);
    
    const toastElement = toastContainer.querySelector('.toast');
    const bsToast = new bootstrap.Toast(toastElement, {
        delay: 3000
    });
    bsToast.show();
    
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastContainer.remove();
    });
}

// 在页面加载时初始化表单验证
document.addEventListener('DOMContentLoaded', function() {
    // 阻止表单默认提交行为
    const form = document.getElementById('create-case-form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // 实时验证用例名称格式
    const caseNameInput = document.getElementById('case-name');
    caseNameInput.addEventListener('input', function() {
        const pattern = /[A-Z]+_[A-Z0-9]+_[A-Z0-9]+_[0-9]{3}/;
        if (this.value && !pattern.test(this.value)) {
            this.setCustomValidity('请按正确格式输入用例名称');
        } else {
            this.setCustomValidity('');
        }
    });

    // 初始化所有tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });
});
</script>
{% endblock %}