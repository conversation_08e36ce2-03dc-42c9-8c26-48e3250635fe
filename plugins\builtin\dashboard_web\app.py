"""
Flask应用主文件

该模块创建和配置Flask应用，注册蓝图，设置路由和中间件。
"""

import os
import sys
import logging
from flask import Flask, render_template, request, jsonify, g
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 确保能找到models模块
models_dir = os.path.join(current_dir, 'models')
if models_dir not in sys.path:
    sys.path.insert(0, models_dir)

try:
    from models.database import init_database, get_db_manager
except ImportError:
    # 如果导入失败，尝试直接导入
    from database import init_database, get_db_manager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config=None):
    """
    创建Flask应用实例

    Args:
        config: 配置字典，可选

    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)

    # 确保数据库路径始终指向同一个位置，无论工作目录如何变化
    dashboard_web_dir = os.path.dirname(os.path.abspath(__file__))
    database_path = os.path.join(dashboard_web_dir, 'data', 'dashboard.db')
    upload_folder = os.path.join(dashboard_web_dir, 'static', 'uploads')

    # 默认配置
    app.config.update({
        'SECRET_KEY': 'dashboard-secret-key-change-in-production',
        'DATABASE_PATH': database_path,
        'UPLOAD_FOLDER': upload_folder,
        'MAX_CONTENT_LENGTH': 16 * 1024 * 1024,  # 16MB
        'JSON_AS_ASCII': False,  # 支持中文JSON
        'JSONIFY_PRETTYPRINT_REGULAR': True,
        'SERVER_NAME': '127.0.0.1:5001',  # 修复URL构建问题
        'APPLICATION_ROOT': '/',
        'PREFERRED_URL_SCHEME': 'http',
    })

    # 记录数据库路径用于调试
    logger.info(f"数据库路径: {database_path}")
    logger.info(f"上传目录: {upload_folder}")
    logger.info(f"当前工作目录: {os.getcwd()}")
    logger.info(f"dashboard_web目录: {dashboard_web_dir}")

    # 应用自定义配置
    if config:
        app.config.update(config)

    # 确保必要的目录存在
    _ensure_directories(app)

    # 初始化数据库
    if not init_database(app.config['DATABASE_PATH']):
        logger.error("数据库初始化失败")
        raise RuntimeError("数据库初始化失败")

    # 注册错误处理器
    _register_error_handlers(app)

    # 注册上下文处理器
    _register_context_processors(app)

    # 注册主要路由
    _register_main_routes(app)

    # 注册API蓝图
    try:
        # 确保能找到routes模块
        routes_dir = os.path.join(dashboard_web_dir, 'routes')
        models_dir = os.path.join(dashboard_web_dir, 'models')
        utils_dir = os.path.join(dashboard_web_dir, 'utils')

        # 添加必要的路径
        for path in [routes_dir, models_dir, utils_dir]:
            if path not in sys.path:
                sys.path.insert(0, path)

        logger.info(f"添加路径到sys.path: {[routes_dir, models_dir, utils_dir]}")

        # 测试关键模块导入
        logger.info("测试关键模块导入...")

        # 测试数据库模块
        try:
            # 使用importlib直接导入，避免模块冲突
            import importlib.util
            database_file = os.path.join(dashboard_web_dir, 'models', 'database.py')
            spec = importlib.util.spec_from_file_location("dashboard_database_test", database_file)
            database_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(database_module)
            get_db = getattr(database_module, 'get_db')
            logger.info("✅ models.database 导入成功")
        except Exception as e:
            logger.error(f"❌ models.database 导入失败: {e}")
            raise ImportError(f"无法导入数据库模块: {e}")

        # 测试工具模块
        try:
            # 使用importlib直接导入，避免模块冲突
            excel_parser_file = os.path.join(dashboard_web_dir, 'utils', 'excel_parser.py')
            if os.path.exists(excel_parser_file):
                spec = importlib.util.spec_from_file_location("dashboard_excel_parser_test", excel_parser_file)
                excel_parser_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(excel_parser_module)
                TestPlanParser = getattr(excel_parser_module, 'TestPlanParser')
                logger.info("✅ utils.excel_parser 导入成功")
            else:
                logger.warning(f"⚠️ excel_parser.py文件不存在: {excel_parser_file}")
        except Exception as e:
            logger.warning(f"⚠️ utils.excel_parser 导入失败: {e}")

        # 注册API蓝图
        logger.info("开始注册API蓝图...")

        try:
            from routes.api import api_bp
            app.register_blueprint(api_bp, url_prefix='/api')
            logger.info("✅ API蓝图注册成功")
        except ImportError as e:
            logger.error(f"❌ API蓝图导入失败: {e}")
            raise

        # 注册用例管理蓝图
        try:
            from routes.testplan import testplan_bp
            app.register_blueprint(testplan_bp, url_prefix='/api/testplan')
            logger.info("✅ 用例管理蓝图注册成功")
        except ImportError as e:
            logger.error(f"❌ 用例管理蓝图导入失败: {e}")
            raise

        # 注册BUG管理蓝图
        try:
            from routes.bug import bug_bp
            app.register_blueprint(bug_bp, url_prefix='/api')
            logger.info("✅ BUG管理蓝图注册成功")
        except ImportError as e:
            logger.warning(f"⚠️ BUG管理蓝图导入失败: {e}")

        # 注册数据导出蓝图
        try:
            from routes.export import export_bp
            app.register_blueprint(export_bp, url_prefix='/api')
            logger.info("✅ 数据导出蓝图注册成功")
        except ImportError as e:
            logger.warning(f"⚠️ 数据导出蓝图导入失败: {e}")

    except ImportError as e:
        logger.warning(f"蓝图注册失败: {e}")
        logger.warning("将使用备用API端点")
        # 创建简单的API端点作为备用
        _register_fallback_api(app)
    except Exception as e:
        logger.error(f"蓝图注册异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        # 创建简单的API端点作为备用
        _register_fallback_api(app)

    logger.info("Flask应用创建成功")
    return app

def _ensure_directories(app):
    """确保必要的目录存在"""
    # 使用绝对路径确保目录创建正确
    dashboard_web_dir = os.path.dirname(os.path.abspath(__file__))

    directories = [
        os.path.dirname(app.config['DATABASE_PATH']),
        app.config['UPLOAD_FOLDER'],
        os.path.join(dashboard_web_dir, 'static', 'css'),
        os.path.join(dashboard_web_dir, 'static', 'js'),
    ]

    for directory in directories:
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            logger.info(f"创建目录: {directory}")

def _register_error_handlers(app):
    """注册错误处理器"""

    @app.errorhandler(404)
    def not_found_error(error):
        if request.path.startswith('/api/'):
            return jsonify({'error': '接口不存在', 'code': 404}), 404
        return render_template('error.html',
                             error_code=404,
                             error_message='页面不存在'), 404

    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"内部服务器错误: {error}")
        if request.path.startswith('/api/'):
            return jsonify({'error': '内部服务器错误', 'code': 500}), 500
        return render_template('error.html',
                             error_code=500,
                             error_message='内部服务器错误'), 500

    @app.errorhandler(413)
    def too_large(error):
        if request.path.startswith('/api/'):
            return jsonify({'error': '文件过大', 'code': 413}), 413
        return render_template('error.html',
                             error_code=413,
                             error_message='上传文件过大'), 413

def _register_context_processors(app):
    """注册上下文处理器"""

    @app.context_processor
    def inject_common_vars():
        """注入通用模板变量"""
        return {
            'current_time': datetime.now(),
            'app_name': 'RunSim Dashboard',
            'version': '1.0.0'
        }

    @app.before_request
    def before_request():
        """请求前处理"""
        g.start_time = datetime.now()

        # 记录API请求
        if request.path.startswith('/api/'):
            logger.info(f"API请求: {request.method} {request.path}")

    @app.after_request
    def after_request(response):
        """请求后处理"""
        if hasattr(g, 'start_time'):
            duration = (datetime.now() - g.start_time).total_seconds()

            # 记录慢请求
            if duration > 1.0:  # 超过1秒的请求
                logger.warning(f"慢请求: {request.method} {request.path} - {duration:.2f}s")

        # 添加CORS头（仅用于开发）
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'

        return response

def _register_main_routes(app):
    """注册主要路由"""

    @app.route('/')
    def index():
        """仪表板主页"""
        try:
            return render_template('dashboard.html')
        except Exception as e:
            logger.error(f"渲染仪表板页面失败: {e}")
            return render_template('error.html',
                                 error_code=500,
                                 error_message='页面加载失败'), 500

    @app.route('/testplan')
    def testplan():
        """用例管理页面"""
        try:
            return render_template('testplan.html')
        except Exception as e:
            logger.error(f"渲染用例管理页面失败: {e}")
            # 如果完整版失败，尝试简化版本
            try:
                return render_template('testplan_simple.html')
            except Exception as e2:
                logger.error(f"渲染简化版页面也失败: {e2}")
                # 最后的回退方案
                return f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>用例管理 - RunSim 项目仪表板</title>
                    <meta charset="utf-8">
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                </head>
                <body>
                    <div class="container mt-5">
                        <div class="alert alert-warning">
                            <h4>⚠️ 用例管理页面</h4>
                            <p>模板渲染失败，使用基本页面。时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                            <p>错误信息: {str(e)}</p>
                            <a href="/" class="btn btn-primary">返回主页</a>
                        </div>
                    </div>
                </body>
                </html>
                """

    @app.route('/bug')
    def bug():
        """BUG管理页面"""
        try:
            return render_template('bug.html')
        except Exception as e:
            logger.error(f"渲染BUG管理页面失败: {e}")
            # 尝试简化版本
            try:
                return render_template('bug_simple.html')
            except Exception as e2:
                logger.error(f"渲染简化版BUG页面也失败: {e2}")
                return render_template('error.html',
                                     error_code=500,
                                     error_message='页面加载失败'), 500

    @app.route('/health')
    def health_check():
        """健康检查接口"""
        try:
            # 检查数据库连接
            import importlib.util
            dashboard_web_dir = os.path.dirname(os.path.abspath(__file__))
            database_file = os.path.join(dashboard_web_dir, 'models', 'database.py')
            spec = importlib.util.spec_from_file_location("dashboard_database_health", database_file)
            database_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(database_module)
            get_db = getattr(database_module, 'get_db')

            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT 1')

            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'database': 'connected'
            })
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }), 500

    @app.route('/api-test')
    def api_test():
        """API测试页面"""
        try:
            return render_template('api_test.html')
        except Exception as e:
            logger.error(f"渲染API测试页面失败: {e}")
            return f"""
            <!DOCTYPE html>
            <html>
            <head><title>API测试</title></head>
            <body>
                <h1>API测试页面</h1>
                <p>时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>错误: {str(e)}</p>
                <a href="/">返回主页</a>
            </body>
            </html>
            """

    @app.route('/import-test')
    def import_test():
        """导入测试页面"""
        try:
            return render_template('import_test.html')
        except Exception as e:
            logger.error(f"渲染导入测试页面失败: {e}")
            return f"""
            <!DOCTYPE html>
            <html>
            <head><title>导入测试</title></head>
            <body>
                <h1>导入测试页面</h1>
                <p>时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>错误: {str(e)}</p>
                <a href="/">返回主页</a>
            </body>
            </html>
            """

def _register_fallback_api(app):
    """注册备用API端点"""

    @app.route('/api/health')
    def api_health():
        return jsonify({'status': 'ok', 'message': 'API蓝图未加载，使用备用端点'})

    @app.route('/api/testplan/statistics')
    def api_testplan_statistics():
        """备用统计API"""
        try:
            # 简单的统计数据
            return jsonify({
                'success': True,
                'data': {
                    'total_cases': 0,
                    'subsys_pass': 0,
                    'subsys_fail': 0,
                    'subsys_ongoing': 0,
                    'top_pass': 0,
                    'top_fail': 0,
                    'top_ongoing': 0,
                    'post_subsys_pass': 0,
                    'post_subsys_fail': 0,
                    'post_top_pass': 0,
                    'post_top_fail': 0
                },
                'message': '使用备用API，数据为空'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': '获取统计信息失败',
                'message': str(e)
            }), 500

    @app.route('/api/testplan/cases')
    def api_testplan_cases():
        """备用用例列表API"""
        try:
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 50))

            return jsonify({
                'success': True,
                'data': {
                    'cases': [],
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': 0,
                        'total_pages': 0,
                        'has_next': False,
                        'has_prev': False
                    }
                },
                'message': '使用备用API，暂无用例数据'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': '获取用例列表失败',
                'message': str(e)
            }), 500

    @app.route('/api/testplan/template')
    def api_testplan_template():
        """备用模板下载API"""
        return jsonify({
            'success': False,
            'error': '模板下载功能暂不可用',
            'message': '请联系管理员'
        }), 503

    # BUG管理备用API
    @app.route('/api/bugs', methods=['GET', 'POST'])
    def api_bugs():
        """备用BUG API"""
        if request.method == 'GET':
            return jsonify({
                'success': True,
                'data': {
                    'bugs': [],
                    'pagination': {
                        'page': 1,
                        'page_size': 20,
                        'total_count': 0,
                        'total_pages': 0,
                        'has_next': False,
                        'has_prev': False
                    }
                },
                'message': '使用备用API，暂无BUG数据'
            })
        elif request.method == 'POST':
            # 模拟创建成功
            import random
            fake_id = random.randint(1000, 9999)
            return jsonify({
                'success': True,
                'data': {'id': fake_id},
                'message': '使用备用API，BUG创建成功（模拟）'
            })
        else:
            return jsonify({
                'success': False,
                'error': '不支持的HTTP方法',
                'message': f'方法 {request.method} 不被支持'
            }), 405

    @app.route('/api/bugs/statistics')
    def api_bugs_statistics():
        """备用BUG统计API"""
        return jsonify({
            'success': True,
            'data': {
                'total_bugs': 0,
                'status_distribution': {},
                'severity_distribution': {},
                'type_distribution': {},
                'open_bugs': 0,
                'fixed_bugs': 0,
                'closed_bugs': 0
            },
            'message': '使用备用API，统计数据为空'
        })

    @app.route('/api/bugs/trend')
    def api_bugs_trend():
        """备用BUG趋势API"""
        return jsonify({
            'success': True,
            'data': {
                'labels': [],
                'new_bugs': [],
                'fixed_bugs': []
            },
            'message': '使用备用API，趋势数据为空'
        })

    # 仪表盘API备用端点
    @app.route('/api/dashboard/statistics')
    def api_dashboard_statistics():
        """备用仪表盘统计API"""
        return jsonify({
            'cases': {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'running': 0,
                'pass_rate': 0
            },
            'bugs': {
                'total': 0,
                'open': 0,
                'fixed': 0,
                'closed': 0
            },
            'projects': {
                'total': 0
            },
            'timestamp': datetime.now().isoformat()
        })

    @app.route('/api/dashboard/progress')
    def api_dashboard_progress():
        """备用项目进度API"""
        return jsonify({
            'subsys_progress': 0,
            'top_progress': 0,
            'post_subsys_progress': 0,
            'post_top_progress': 0,
            'overall_progress': 0,
            'total_cases': 0,
            'timestamp': datetime.now().isoformat()
        })

    @app.route('/api/dashboard/case_status')
    def api_dashboard_case_status():
        """备用用例状态分布API"""
        return jsonify({
            'pass': 0,
            'fail': 0,
            'ongoing': 0,
            'not_started': 0,
            'timestamp': datetime.now().isoformat()
        })

    @app.route('/api/dashboard/bug_trend')
    def api_dashboard_bug_trend():
        """备用BUG趋势API"""
        return jsonify({
            'labels': [],
            'new_bugs': [],
            'fixed_bugs': [],
            'timestamp': datetime.now().isoformat()
        })

    # 数据导出备用API
    @app.route('/api/dashboard/export/json')
    def api_export_json():
        """备用JSON导出API"""
        export_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'description': 'RunSim Dashboard Export Data (Fallback)'
            },
            'testcase_statistics': {
                'total_testcases': 0,
                'status_distribution': {}
            },
            'bug_statistics': {
                'total_bugs': 0,
                'open_bugs': 0,
                'fixed_bugs': 0,
                'closed_bugs': 0
            },
            'bug_trend': {
                'labels': [],
                'new_bugs': [],
                'fixed_bugs': []
            },
            'progress_data': {
                'subsys_progress': 0,
                'top_progress': 0,
                'post_subsys_progress': 0,
                'post_top_progress': 0
            },
            'summary': {
                'total_testcases': 0,
                'total_bugs': 0,
                'completion_rate': 0,
                'bug_fix_rate': 0
            }
        }

        return jsonify({
            'success': True,
            'data': export_data
        })

    @app.route('/api/dashboard/export/excel', methods=['POST'])
    def api_export_excel():
        """备用Excel导出API"""
        return jsonify({
            'success': False,
            'message': '缺少openpyxl库，无法生成Excel报告。请安装: pip install openpyxl'
        }), 500

    @app.route('/api/dashboard/export/pdf', methods=['POST'])
    def api_export_pdf():
        """备用PDF导出API"""
        return jsonify({
            'success': False,
            'message': '缺少reportlab库，无法生成PDF报告。请安装: pip install reportlab'
        }), 500

    logger.info("备用API端点注册成功")

# 用于开发测试的简单启动函数
def run_dev_server():
    """运行开发服务器"""
    app = create_app()
    app.run(host='127.0.0.1', port=5001, debug=True)

if __name__ == '__main__':
    run_dev_server()
