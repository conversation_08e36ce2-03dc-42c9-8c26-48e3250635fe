/**
 * RunSim Dashboard JavaScript
 *
 * 仪表板核心功能脚本
 */

// 全局变量
let dashboardCharts = {};
let refreshInterval = null;
let connectionStatus = false;

// 配置常量
const CONFIG = {
    REFRESH_INTERVAL: 30000, // 30秒
    CHART_COLORS: {
        primary: '#007bff',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8',
        secondary: '#6c757d'
    },
    ANIMATION_DURATION: 1000
};

/**
 * 初始化仪表板
 */
function initializeDashboard() {
    console.log('初始化仪表板...');

    // 初始化图表
    initializeCharts();

    // 加载初始数据
    loadDashboardData();

    // 设置自动刷新
    setupAutoRefresh();

    // 绑定事件监听器
    bindEventListeners();

    // 初始化工具提示
    initializeTooltips();

    console.log('仪表板初始化完成');
}

/**
 * 初始化图表
 */
function initializeCharts() {
    // 状态分布饼图
    initializeStatusChart();

    // 进度条形图
    initializeProgressChart();

    // BUG趋势线图
    initializeBugTrendChart();

    // 阶段效率雷达图
    initializePhaseEfficiencyChart();
}

/**
 * 初始化状态分布图表
 */
function initializeStatusChart() {
    const ctx = document.getElementById('statusChart');
    if (!ctx) return;

    dashboardCharts.statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['通过', '待处理', '进行中', '未开始'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    CONFIG.CHART_COLORS.success,
                    CONFIG.CHART_COLORS.info,
                    CONFIG.CHART_COLORS.warning,
                    CONFIG.CHART_COLORS.secondary
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: CONFIG.ANIMATION_DURATION
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = this.data.labels[index];
                    showStatusDetails(label, index);
                }
            }
        }
    });
}

/**
 * 初始化进度图表
 */
function initializeProgressChart() {
    const ctx = document.getElementById('progressChart');
    if (!ctx) return;

    dashboardCharts.progressChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['子系统级', 'TOP级', '后仿子系统', '后仿TOP'],
            datasets: [{
                label: '完成进度 (%)',
                data: [0, 0, 0, 0],
                backgroundColor: [
                    CONFIG.CHART_COLORS.primary,
                    CONFIG.CHART_COLORS.success,
                    CONFIG.CHART_COLORS.warning,
                    CONFIG.CHART_COLORS.info
                ],
                borderColor: [
                    CONFIG.CHART_COLORS.primary,
                    CONFIG.CHART_COLORS.success,
                    CONFIG.CHART_COLORS.warning,
                    CONFIG.CHART_COLORS.info
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: CONFIG.ANIMATION_DURATION
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed.y}%`;
                        }
                    }
                }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = this.data.labels[index];
                    const value = this.data.datasets[0].data[index];
                    showProgressDetails(label, value);
                }
            }
        }
    });
}

/**
 * 初始化BUG趋势图表
 */
function initializeBugTrendChart() {
    const ctx = document.getElementById('bugTrendChart');
    if (!ctx) return;

    dashboardCharts.bugTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '新增BUG',
                data: [],
                borderColor: CONFIG.CHART_COLORS.danger,
                backgroundColor: CONFIG.CHART_COLORS.danger + '20',
                tension: 0.4,
                fill: true
            }, {
                label: '修复BUG',
                data: [],
                borderColor: CONFIG.CHART_COLORS.success,
                backgroundColor: CONFIG.CHART_COLORS.success + '20',
                tension: 0.4,
                fill: true
            }, {
                label: 'BUG总数',
                data: [],
                borderColor: CONFIG.CHART_COLORS.warning,
                backgroundColor: 'transparent',
                tension: 0.4,
                fill: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: CONFIG.ANIMATION_DURATION
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    title: {
                        display: true,
                        text: '新增/修复数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: 'BUG总数'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return '日期: ' + context[0].label;
                        }
                    }
                }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const date = this.data.labels[index];
                    showBugTrendDetails(date, index);
                }
            }
        }
    });
}

/**
 * 初始化阶段效率雷达图
 */
function initializePhaseEfficiencyChart() {
    const ctx = document.getElementById('phaseEfficiencyChart');
    if (!ctx) return;

    dashboardCharts.phaseEfficiencyChart = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2'],
            datasets: [{
                label: '阶段效率 (%)',
                data: [0, 0, 0, 0, 0],
                borderColor: CONFIG.CHART_COLORS.primary,
                backgroundColor: CONFIG.CHART_COLORS.primary + '20',
                pointBackgroundColor: CONFIG.CHART_COLORS.primary,
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: CONFIG.CHART_COLORS.primary
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: CONFIG.ANIMATION_DURATION
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20,
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

/**
 * 加载仪表板数据
 */
function loadDashboardData() {
    showLoadingIndicator();

    Promise.all([
        loadStatisticsData(),
        loadProgressData(),
        loadBugTrendData(),
        loadPhaseEfficiencyData()
    ]).then(() => {
        hideLoadingIndicator();
        updateConnectionStatus(true);
        updateLastRefreshTime();
    }).catch(error => {
        console.error('加载仪表板数据失败:', error);
        hideLoadingIndicator();
        updateConnectionStatus(false);
        showErrorMessage('数据加载失败，请检查网络连接');
    });
}

/**
 * 加载统计数据
 */
function loadStatisticsData() {
    return fetch('/api/dashboard/statistics')
        .then(response => response.json())
        .then(data => {
            updateStatisticsCards(data.cases);
            updateStatusChart(data.cases);
        });
}

/**
 * 加载进度数据
 */
function loadProgressData() {
    return fetch('/api/dashboard/progress')
        .then(response => response.json())
        .then(data => {
            updateProgressChart(data);
        });
}

/**
 * 加载BUG趋势数据
 */
function loadBugTrendData() {
    return fetch('/api/dashboard/bug_trend')
        .then(response => response.json())
        .then(data => {
            updateBugTrendChart(data);
        });
}

/**
 * 加载阶段效率数据
 */
function loadPhaseEfficiencyData() {
    return fetch('/api/dashboard/phase_efficiency')
        .then(response => response.json())
        .then(data => {
            updatePhaseEfficiencyChart(data);
        })
        .catch(error => {
            console.warn('阶段效率数据加载失败:', error);
            // 使用模拟数据
            updatePhaseEfficiencyChart({
                efficiency: [85, 78, 92, 88, 76]
            });
        });
}

/**
 * 更新统计卡片
 */
function updateStatisticsCards(data) {
    if (!data) return;

    // 更新数值
    updateElement('total-cases', data.total || 0);
    updateElement('passed-cases', data.passed || 0);
    updateElement('pending-cases', data.pending || 0);
    updateElement('running-cases', data.running || 0);
    updateElement('pass-rate', (data.pass_rate || 0) + '%');

    // 更新分级别统计
    if (data.subsys) {
        updateElement('subsys-passed', data.subsys.passed || 0);
        updateElement('subsys-pending', data.subsys.pending || 0);
        updateElement('subsys-running', data.subsys.running || 0);
    }

    if (data.top) {
        updateElement('top-passed', data.top.passed || 0);
        updateElement('top-pending', data.top.pending || 0);
        updateElement('top-running', data.top.running || 0);
    }
}

/**
 * 更新状态图表
 */
function updateStatusChart(data) {
    if (!dashboardCharts.statusChart || !data) return;

    const chart = dashboardCharts.statusChart;
    chart.data.datasets[0].data = [
        data.passed || 0,
        data.pending || 0,
        data.running || 0,
        data.not_started || 0
    ];
    chart.update('none');
}

/**
 * 更新进度图表
 */
function updateProgressChart(data) {
    if (!dashboardCharts.progressChart || !data) return;

    const chart = dashboardCharts.progressChart;
    chart.data.datasets[0].data = [
        data.subsys_progress || 0,
        data.top_progress || 0,
        data.post_subsys_progress || 0,
        data.post_top_progress || 0
    ];
    chart.update('none');
}

/**
 * 更新BUG趋势图表
 */
function updateBugTrendChart(data) {
    if (!dashboardCharts.bugTrendChart || !data) return;

    const chart = dashboardCharts.bugTrendChart;

    // 格式化标签
    let labels = data.labels || [];
    if (data.unit === 'week') {
        labels = labels.map(label => {
            const weekMatch = label.match(/(\d{4})-W(\d{2})/);
            if (weekMatch) {
                return `第${parseInt(weekMatch[2])}周`;
            }
            return label;
        });
    }

    // 计算累计BUG总数
    const newBugs = data.new_bugs || [];
    const fixedBugs = data.fixed_bugs || [];
    const totalBugs = [];

    let cumulativeTotal = data.initial_total || 0;
    for (let i = 0; i < newBugs.length; i++) {
        cumulativeTotal += (newBugs[i] || 0) - (fixedBugs[i] || 0);
        totalBugs.push(Math.max(0, cumulativeTotal));
    }

    chart.data.labels = labels;
    chart.data.datasets[0].data = newBugs;
    chart.data.datasets[1].data = fixedBugs;
    chart.data.datasets[2].data = totalBugs;
    chart.update('none');
}

/**
 * 更新阶段效率图表
 */
function updatePhaseEfficiencyChart(data) {
    if (!dashboardCharts.phaseEfficiencyChart || !data) return;

    const chart = dashboardCharts.phaseEfficiencyChart;
    chart.data.datasets[0].data = data.efficiency || [0, 0, 0, 0, 0];
    chart.update('none');
}

/**
 * 设置自动刷新
 */
function setupAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }

    refreshInterval = setInterval(() => {
        loadDashboardData();
    }, CONFIG.REFRESH_INTERVAL);
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 刷新按钮
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            loadDashboardData();
        });
    }

    // BUG趋势切换控件
    setupBugTrendControls();

    // 窗口失焦/获焦事件
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            // 页面隐藏时停止刷新
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        } else {
            // 页面显示时恢复刷新
            setupAutoRefresh();
            loadDashboardData();
        }
    });
}

/**
 * 设置BUG趋势控件
 */
function setupBugTrendControls() {
    // 时间单位切换
    const unitControls = document.querySelectorAll('input[name="trendUnit"]');
    unitControls.forEach(control => {
        control.addEventListener('change', () => {
            const unit = control.value;
            const chartType = document.querySelector('input[name="chartType"]:checked')?.value || 'line';
            switchBugTrendChart(unit, chartType);
        });
    });

    // 图表类型切换
    const typeControls = document.querySelectorAll('input[name="chartType"]');
    typeControls.forEach(control => {
        control.addEventListener('change', () => {
            const chartType = control.value;
            const unit = document.querySelector('input[name="trendUnit"]:checked')?.value || 'day';
            switchBugTrendChart(unit, chartType);
        });
    });
}

/**
 * 切换BUG趋势图表
 */
function switchBugTrendChart(unit, chartType) {
    // 销毁现有图表
    if (dashboardCharts.bugTrendChart) {
        dashboardCharts.bugTrendChart.destroy();
    }

    // 重新初始化图表
    initializeBugTrendChart();

    // 加载新数据
    loadBugTrendData();
}

/**
 * 工具函数
 */

/**
 * 更新元素内容
 */
function updateElement(id, content) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = content;
    }
}

/**
 * 显示加载指示器
 */
function showLoadingIndicator() {
    const indicator = document.querySelector('.refresh-indicator');
    if (indicator) {
        indicator.style.display = 'block';
        indicator.innerHTML = '<i class="loading-spinner"></i> 加载中...';
    }
}

/**
 * 隐藏加载指示器
 */
function hideLoadingIndicator() {
    const indicator = document.querySelector('.refresh-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

/**
 * 更新连接状态
 */
function updateConnectionStatus(online) {
    connectionStatus = online;
    const indicator = document.querySelector('.connection-status');
    if (indicator) {
        indicator.className = `connection-status ${online ? 'status-online' : 'status-offline'}`;
    }

    const text = document.getElementById('connection-text');
    if (text) {
        text.textContent = online ? '已连接' : '连接断开';
    }
}

/**
 * 更新最后刷新时间
 */
function updateLastRefreshTime() {
    const timeElements = document.querySelectorAll('[id$="-update"]');
    const currentTime = new Date().toLocaleTimeString();

    timeElements.forEach(element => {
        element.textContent = `更新于 ${currentTime}`;
    });
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    // 创建错误提示
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show';
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '1060';
    alert.style.minWidth = '300px';

    alert.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    // 5秒后自动移除
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    // 初始化Bootstrap工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 显示状态详情
 */
function showStatusDetails(label, index) {
    const data = dashboardCharts.statusChart.data.datasets[0].data;
    const total = data.reduce((a, b) => a + b, 0);
    const value = data[index];
    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;

    showModal('用例状态详情', `
        <div class="text-center">
            <h4 class="text-primary">${label}</h4>
            <p class="h2 mb-3">${value}</p>
            <p class="text-muted">占总数的 ${percentage}%</p>
            <hr>
            <a href="/testplan?status=${encodeURIComponent(label)}" class="btn btn-primary">
                <i class="fas fa-list me-1"></i>查看详细用例
            </a>
        </div>
    `);
}

/**
 * 显示进度详情
 */
function showProgressDetails(label, value) {
    showModal('项目进度详情', `
        <div class="text-center">
            <h4 class="text-info">${label}</h4>
            <div class="progress mb-3" style="height: 20px;">
                <div class="progress-bar" role="progressbar" style="width: ${value}%">${value}%</div>
            </div>
            <p class="text-muted">当前完成进度</p>
            <hr>
            <a href="/testplan?stage=${encodeURIComponent(label)}" class="btn btn-info">
                <i class="fas fa-tasks me-1"></i>查看相关用例
            </a>
        </div>
    `);
}

/**
 * 显示BUG趋势详情
 */
function showBugTrendDetails(date, index) {
    const chart = dashboardCharts.bugTrendChart;
    const newBugs = chart.data.datasets[0].data[index] || 0;
    const fixedBugs = chart.data.datasets[1].data[index] || 0;
    const totalBugs = chart.data.datasets[2].data[index] || 0;

    showModal('BUG趋势详情', `
        <div class="text-center">
            <h4 class="text-warning">${date}</h4>
            <div class="row">
                <div class="col-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <h5>新增BUG</h5>
                            <h2>${newBugs}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5>修复BUG</h5>
                            <h2>${fixedBugs}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <h5>BUG总数</h5>
                            <h2>${totalBugs}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <p class="text-muted">
                    净变化: ${newBugs - fixedBugs > 0 ? '+' : ''}${newBugs - fixedBugs}
                </p>
            </div>
            <hr>
            <a href="/bug?date=${date}" class="btn btn-warning">
                <i class="fas fa-bug me-1"></i>查看当日BUG
            </a>
        </div>
    `);
}

/**
 * 显示模态框
 */
function showModal(title, content) {
    const modalHtml = `
        <div class="modal fade" id="detailModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('detailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框并显示
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
    modal.show();

    // 模态框关闭后移除
    document.getElementById('detailModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// 导出全局函数供其他脚本使用
window.DashboardAPI = {
    refresh: loadDashboardData,
    updateStatistics: updateStatisticsCards,
    updateCharts: function(data) {
        updateStatusChart(data.status);
        updateProgressChart(data.progress);
        updateBugTrendChart(data.bugTrend);
    },
    showError: showErrorMessage,
    showModal: showModal
};
