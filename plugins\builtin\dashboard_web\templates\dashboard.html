{% extends "base.html" %}

{% block title %}仪表板 - RunSim 项目仪表板{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin: 0;
    }
    .chart-card {
        height: 400px;
    }
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
    .refresh-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        display: none;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .status-online {
        background-color: #28a745;
        animation: pulse 2s infinite;
    }
    .status-offline {
        background-color: #dc3545;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    .performance-panel {
        position: fixed;
        top: 80px;
        right: 20px;
        width: 300px;
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .performance-panel .card {
        border: 1px solid #dee2e6;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
    }
    .performance-panel .card-body {
        font-size: 0.8rem;
    }
    .performance-panel .progress {
        margin-top: 2px;
        margin-bottom: 2px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 刷新指示器 -->
<div class="refresh-indicator">
    <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

<!-- 性能监控面板 -->
<div class="performance-panel" id="performance-panel" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-chart-area me-1"></i>性能监控
            </h6>
            <button class="btn btn-sm btn-outline-secondary" onclick="togglePerformancePanel()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="card-body p-2">
            <div class="row g-2">
                <div class="col-6">
                    <small class="text-muted">连接状态:</small>
                    <div id="perf-connection-status">-</div>
                </div>
                <div class="col-6">
                    <small class="text-muted">刷新频率:</small>
                    <div id="perf-refresh-rate">-</div>
                </div>
                <div class="col-6">
                    <small class="text-muted">最后更新:</small>
                    <div id="perf-last-update">-</div>
                </div>
                <div class="col-6">
                    <small class="text-muted">重试次数:</small>
                    <div id="perf-retry-count">-</div>
                </div>
                <div class="col-12">
                    <small class="text-muted">页面性能:</small>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-info" id="perf-memory-bar" style="width: 0%"></div>
                    </div>
                    <small id="perf-memory-text">内存使用: -</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 页面标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">项目仪表板</h1>
                <p class="text-muted mb-0">实时监控项目进度和用例执行状态</p>
            </div>
            <div class="d-flex align-items-center">
                <span class="status-indicator status-online" id="connection-status"></span>
                <small class="text-muted me-3" id="connection-text">实时连接</small>
                <button class="btn btn-outline-secondary btn-sm me-2" onclick="togglePerformancePanel()" title="性能监控">
                    <i class="fas fa-chart-area"></i>
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">总用例数</p>
                        <h2 class="metric-value" id="total-cases">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list-check fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">通过用例</p>
                        <h2 class="metric-value" id="passed-cases">-</h2>
                        <small class="text-white-50">通过率: <span id="pass-rate">-</span>%</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">待处理用例</p>
                        <h2 class="metric-value" id="pending-cases">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hourglass-half fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">进行中</p>
                        <h2 class="metric-value" id="running-cases">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分级别统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-6 mb-3">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>Subsys级别统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success mb-1" id="subsys-passed">-</h4>
                            <small class="text-muted">通过</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-info mb-1" id="subsys-pending">-</h4>
                            <small class="text-muted">待处理</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning mb-1" id="subsys-running">-</h4>
                        <small class="text-muted">进行中</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-3">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>TOP级别统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success mb-1" id="top-passed">-</h4>
                            <small class="text-muted">通过</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-info mb-1" id="top-pending">-</h4>
                            <small class="text-muted">待处理</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning mb-1" id="top-running">-</h4>
                        <small class="text-muted">进行中</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 用例状态分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>用例状态分布
                </h5>
                <small class="text-muted" id="status-chart-update">-</small>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目进度 -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>项目进度
                </h5>
                <small class="text-muted" id="progress-chart-update">-</small>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="progressChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- BUG趋势图 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card chart-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>BUG趋势
                </h5>
                <div class="d-flex align-items-center">
                    <!-- 时间单位切换 -->
                    <div class="btn-group btn-group-sm me-3" role="group">
                        <input type="radio" class="btn-check" name="trendUnit" id="trendDay" value="day" checked>
                        <label class="btn btn-outline-primary" for="trendDay">按天</label>

                        <input type="radio" class="btn-check" name="trendUnit" id="trendWeek" value="week">
                        <label class="btn btn-outline-primary" for="trendWeek">按周</label>
                    </div>

                    <!-- 图表类型切换 -->
                    <div class="btn-group btn-group-sm me-3" role="group">
                        <input type="radio" class="btn-check" name="chartType" id="chartLine" value="line" checked>
                        <label class="btn btn-outline-secondary" for="chartLine">
                            <i class="fas fa-chart-line"></i>
                        </label>

                        <input type="radio" class="btn-check" name="chartType" id="chartBar" value="bar">
                        <label class="btn btn-outline-secondary" for="chartBar">
                            <i class="fas fa-chart-bar"></i>
                        </label>
                    </div>

                    <small class="text-muted" id="bug-trend-update">-</small>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="bugTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 验证阶段管理 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>验证阶段管理
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary me-2" onclick="refreshPhaseData()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <small class="text-muted" id="phase-update">-</small>
                </div>
            </div>
            <div class="card-body">
                <!-- 阶段进度概览 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">阶段进度概览</h6>
                        <div id="phase-progress-container">
                            <!-- 动态生成阶段进度条 -->
                        </div>
                    </div>
                </div>

                <!-- 阶段详细统计 -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="mb-3">阶段详细统计</h6>
                        <div class="table-responsive">
                            <table class="table table-hover" id="phase-statistics-table">
                                <thead class="table-light">
                                    <tr>
                                        <th>验证阶段</th>
                                        <th>Subsys级</th>
                                        <th>TOP级</th>
                                        <th>POST_Subsys</th>
                                        <th>POST_TOP</th>
                                        <th>总计</th>
                                        <th>进度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态生成统计数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <a href="/testplan" class="btn btn-outline-primary w-100">
                            <i class="fas fa-upload me-2"></i>导入TestPlan
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="/bug" class="btn btn-outline-warning w-100">
                            <i class="fas fa-plus me-2"></i>添加BUG记录
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="exportReport()">
                            <i class="fas fa-download me-2"></i>导出报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/error-handler.js') }}"></script>
<script src="{{ url_for('static', filename='js/realtime.js') }}"></script>
<script>
// 图表变量现在使用 window 对象存储，避免重复初始化问题
// refreshInterval 已在 dashboard.js 中声明，这里不重复声明

$(document).ready(function() {
    // 初始化图表
    initCharts();

    // 设置实时更新管理器
    setupRealtimeUpdates();

    // 设置BUG趋势图切换事件
    setupBugTrendControls();

    // 加载初始数据
    loadDashboardData();
});

function setupBugTrendControls() {
    console.log('设置BUG趋势图控制事件');

    // 时间单位切换事件 - 使用更具体的选择器
    $(document).on('change', 'input[name="trendUnit"]', function() {
        const unit = $(this).val();
        const chartType = $('input[name="chartType"]:checked').val() || 'line';
        console.log('时间单位切换:', unit, '图表类型:', chartType);
        switchTrendChart(unit, chartType);
    });

    // 图表类型切换事件 - 使用更具体的选择器
    $(document).on('change', 'input[name="chartType"]', function() {
        const chartType = $(this).val();
        const unit = $('input[name="trendUnit"]:checked').val() || 'day';
        console.log('图表类型切换:', chartType, '时间单位:', unit);
        switchTrendChart(unit, chartType);
    });

    // 添加按钮点击事件（如果有按钮形式的控制）
    $(document).on('click', '.bug-trend-unit-btn', function() {
        const unit = $(this).data('unit');
        const chartType = $('.bug-trend-type-btn.active').data('type') || 'line';
        $('.bug-trend-unit-btn').removeClass('active');
        $(this).addClass('active');
        console.log('按钮切换时间单位:', unit, '图表类型:', chartType);
        switchTrendChart(unit, chartType);
    });

    $(document).on('click', '.bug-trend-type-btn', function() {
        const chartType = $(this).data('type');
        const unit = $('.bug-trend-unit-btn.active').data('unit') || 'day';
        $('.bug-trend-type-btn').removeClass('active');
        $(this).addClass('active');
        console.log('按钮切换图表类型:', chartType, '时间单位:', unit);
        switchTrendChart(unit, chartType);
    });
}

function setupRealtimeUpdates() {
    // 监听全局刷新事件
    document.addEventListener('dashboard:refresh', function(event) {
        console.log('收到数据刷新事件:', event.detail);
        loadDashboardData();
    });

    // 注册数据变化回调
    if (window.realtimeManager) {
        // 统计数据变化
        window.realtimeManager.onDataChange('statistics', function(data) {
            console.log('收到统计数据更新:', data);
            updateStatistics(data);
        });

        // 用例状态变化
        window.realtimeManager.onDataChange('case_status', function(data) {
            console.log('收到用例状态更新:', data);
            updateStatusChart(data);
            $('#status-chart-update').text('实时更新于 ' + new Date().toLocaleTimeString());
        });

        // 项目进度变化
        window.realtimeManager.onDataChange('progress', function(data) {
            console.log('收到进度数据更新:', data);
            updateProgressChart(data);
            $('#progress-chart-update').text('实时更新于 ' + new Date().toLocaleTimeString());
        });

        // BUG趋势变化
        window.realtimeManager.onDataChange('bug_trend', function(data) {
            console.log('收到BUG趋势更新:', data);
            updateBugTrendChart(data);
            $('#bug-trend-update').text('实时更新于 ' + new Date().toLocaleTimeString());
        });

        // 连接状态变化
        window.realtimeManager.onConnectionStatusChange(function(isConnected) {
            updateConnectionStatus(isConnected);
            updateConnectionInfo(isConnected);
        });
    }
}

// 销毁现有图表的函数
function destroyExistingCharts() {
    console.log('销毁现有图表...');

    // 销毁状态图表
    try {
        // 使用Chart.js的getChart方法获取现有图表
        const existingStatusChart = Chart.getChart('statusChart');
        if (existingStatusChart) {
            existingStatusChart.destroy();
            console.log('状态图表已销毁');
        }

        // 如果window.statusChart存在且有destroy方法，也尝试销毁
        if (window.statusChart && typeof window.statusChart.destroy === 'function') {
            window.statusChart.destroy();
        }
        window.statusChart = null;
    } catch (error) {
        console.warn('销毁状态图表失败:', error);
        window.statusChart = null;
    }

    // 销毁进度图表
    try {
        const existingProgressChart = Chart.getChart('progressChart');
        if (existingProgressChart) {
            existingProgressChart.destroy();
            console.log('进度图表已销毁');
        }

        if (window.progressChart && typeof window.progressChart.destroy === 'function') {
            window.progressChart.destroy();
        }
        window.progressChart = null;
    } catch (error) {
        console.warn('销毁进度图表失败:', error);
        window.progressChart = null;
    }

    // 销毁BUG趋势图表
    try {
        const existingBugTrendChart = Chart.getChart('bugTrendChart');
        if (existingBugTrendChart) {
            existingBugTrendChart.destroy();
            console.log('BUG趋势图表已销毁');
        }

        if (window.bugTrendChart && typeof window.bugTrendChart.destroy === 'function') {
            window.bugTrendChart.destroy();
        }
        window.bugTrendChart = null;
    } catch (error) {
        console.warn('销毁BUG趋势图表失败:', error);
        window.bugTrendChart = null;
    }
}

function initCharts() {
    console.log('开始初始化图表...');

    // 销毁现有图表（如果存在）
    destroyExistingCharts();

    // 用例状态分布图表
    try {
        const statusCtx = document.getElementById('statusChart');
        if (!statusCtx) {
            console.error('找不到状态图表Canvas元素');
            return;
        }

        const ctx = statusCtx.getContext('2d');
        window.statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['通过', '待处理', '进行中', '未开始'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#6c757d'],
                borderWidth: 2,
                borderColor: '#fff',
                hoverBackgroundColor: ['#34ce57', '#1f8fa3', '#ffcd39', '#7a8288'],
                hoverBorderWidth: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    },
                    onHover: function(event, legendItem, legend) {
                        legend.chart.canvas.style.cursor = 'pointer';
                    },
                    onLeave: function(event, legendItem, legend) {
                        legend.chart.canvas.style.cursor = 'default';
                    },
                    onClick: function(event, legendItem, legend) {
                        showStatusDetails(legendItem.text, legendItem.index);
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            onHover: function(event, elements) {
                event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = this.data.labels[index];
                    showStatusDetails(label, index);
                }
            }
        }
        });
        console.log('状态图表初始化成功');
    } catch (error) {
        console.error('状态图表初始化失败:', error);
    }

    // 项目进度图表
    try {
        const progressCtx = document.getElementById('progressChart');
        if (!progressCtx) {
            console.error('找不到进度图表Canvas元素');
            return;
        }

        const ctx2 = progressCtx.getContext('2d');
        window.progressChart = new Chart(ctx2, {
        type: 'bar',
        data: {
            labels: ['子系统级', 'TOP级', '后仿子系统', '后仿TOP'],
            datasets: [{
                label: '完成进度 (%)',
                data: [0, 0, 0, 0],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#17a2b8'],
                borderColor: ['#0056b3', '#1e7e34', '#e0a800', '#117a8b'],
                borderWidth: 1,
                hoverBackgroundColor: ['#0056b3', '#1e7e34', '#e0a800', '#117a8b'],
                hoverBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed.y}%`;
                        }
                    }
                }
            },
            onHover: function(event, elements) {
                event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = this.data.labels[index];
                    const value = this.data.datasets[0].data[index];
                    showProgressDetails(label, value);
                }
            }
        }
        });
        console.log('进度图表初始化成功');
    } catch (error) {
        console.error('进度图表初始化失败:', error);
    }

    // BUG趋势图表
    try {
        const bugTrendCtx = document.getElementById('bugTrendChart');
        if (!bugTrendCtx) {
            console.error('找不到BUG趋势图表Canvas元素');
            return;
        }

        const ctx3 = bugTrendCtx.getContext('2d');
        window.bugTrendChart = new Chart(ctx3, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '新增BUG',
                data: [],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#dc3545',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }, {
                label: '修复BUG',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#28a745',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }, {
                label: 'BUG总数',
                data: [],
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4,
                fill: false,
                pointBackgroundColor: '#ffc107',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1  // 确保Y轴显示整数
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    title: {
                        display: true,
                        text: '新增/修复数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1  // 确保Y轴显示整数
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'BUG总数'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return '日期: ' + context[0].label;
                        },
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y}`;
                        }
                    }
                }
            },
            onHover: function(event, elements) {
                event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const date = this.data.labels[index];
                    showBugTrendDetails(date, index);
                }
            }
        }
        });
        console.log('BUG趋势图表初始化成功');
    } catch (error) {
        console.error('BUG趋势图表初始化失败:', error);
    }

    console.log('所有图表初始化完成');
}

function loadDashboardData() {
    showRefreshIndicator();

    // 加载统计数据
    $.get('/api/dashboard/statistics')
        .done(function(data) {
            updateStatistics(data);
            updateConnectionStatus(true);
        })
        .fail(function() {
            updateConnectionStatus(false);
        });

    // 加载项目进度数据
    $.get('/api/dashboard/progress')
        .done(function(data) {
            updateProgressChart(data);
            $('#progress-chart-update').text('更新于 ' + new Date().toLocaleTimeString());
        });

    // 加载用例状态分布数据
    $.get('/api/dashboard/case_status')
        .done(function(data) {
            updateStatusChart(data);
            $('#status-chart-update').text('更新于 ' + new Date().toLocaleTimeString());
        });

    // 加载BUG趋势数据
    $.get('/api/dashboard/bug_trend')
        .done(function(data) {
            updateBugTrendChart(data);
            $('#bug-trend-update').text('更新于 ' + new Date().toLocaleTimeString());
        });

    // 加载验证阶段数据
    loadPhaseData();

    hideRefreshIndicator();
}

function updateStatistics(data) {
    const cases = data.cases || {};
    $('#total-cases').text(cases.total || 0);
    $('#passed-cases').text(cases.passed || 0);
    $('#pending-cases').text(cases.pending || 0);  // 确保更新待处理状态
    $('#running-cases').text(cases.running || 0);
    $('#pass-rate').text(cases.pass_rate || 0);

    // 如果页面支持分级别显示，更新分级别统计
    if (cases.subsys && $('#subsys-passed').length) {
        $('#subsys-passed').text(cases.subsys.passed || 0);
        $('#subsys-pending').text(cases.subsys.pending || 0);
        $('#subsys-running').text(cases.subsys.running || 0);
    }

    if (cases.top && $('#top-passed').length) {
        $('#top-passed').text(cases.top.passed || 0);
        $('#top-pending').text(cases.top.pending || 0);
        $('#top-running').text(cases.top.running || 0);
    }
}

function updateStatusChart(data) {
    // 数据验证
    if (!data) {
        console.warn('updateStatusChart: 数据为空');
        return;
    }

    // 图表状态检查
    if (!window.statusChart || !window.statusChart.data || !window.statusChart.data.datasets || !window.statusChart.data.datasets[0]) {
        console.warn('updateStatusChart: 状态图表未初始化');
        return;
    }

    try {
        window.statusChart.data.datasets[0].data = [
            data.pass || 0,
            data.pending || 0,  // 修正为pending状态
            data.ongoing || 0,
            data.not_started || 0
        ];
        window.statusChart.update('none');
        console.log('状态图表更新成功');
    } catch (error) {
        console.error('更新状态图表失败:', error);
    }
}

function updateProgressChart(data) {
    // 数据验证
    if (!data) {
        console.warn('updateProgressChart: 数据为空');
        return;
    }

    // 图表状态检查
    if (!window.progressChart || !window.progressChart.data || !window.progressChart.data.datasets || !window.progressChart.data.datasets[0]) {
        console.warn('updateProgressChart: 进度图表未初始化');
        return;
    }

    try {
        window.progressChart.data.datasets[0].data = [
            data.subsys_progress || 0,
            data.top_progress || 0,
            data.post_subsys_progress || 0,
            data.post_top_progress || 0
        ];
        window.progressChart.update('none');
        console.log('进度图表更新成功');
    } catch (error) {
        console.error('更新进度图表失败:', error);
    }
}

function updateBugTrendChart(data) {
    // 数据验证
    if (!data) {
        console.warn('updateBugTrendChart: 数据为空');
        return;
    }

    // 图表状态检查
    if (!window.bugTrendChart || !window.bugTrendChart.data || !window.bugTrendChart.data.datasets) {
        console.warn('updateBugTrendChart: BUG趋势图表未初始化');
        return;
    }

    try {
        // 格式化标签（如果是周数据，显示更友好的格式）
        let labels = data.labels || [];
        if (data.unit === 'week') {
            labels = labels.map(label => {
                // 将 "2024-W01" 格式转换为 "第1周"
                const weekMatch = label.match(/(\d{4})-W(\d{2})/);
                if (weekMatch) {
                    return `第${parseInt(weekMatch[2])}周`;
                }
                return label;
            });
        }

        // 计算累计BUG总数
        const newBugs = data.new_bugs || [];
        const fixedBugs = data.fixed_bugs || [];
        const totalBugs = [];

        let cumulativeTotal = data.initial_total || 0;  // 初始BUG总数

        for (let i = 0; i < newBugs.length; i++) {
            cumulativeTotal += (newBugs[i] || 0) - (fixedBugs[i] || 0);
            totalBugs.push(Math.max(0, cumulativeTotal));  // 确保总数不为负
        }

        // 安全更新图表数据
        window.bugTrendChart.data.labels = labels;

        if (window.bugTrendChart.data.datasets[0]) {
            window.bugTrendChart.data.datasets[0].data = newBugs;
        }

        if (window.bugTrendChart.data.datasets[1]) {
            window.bugTrendChart.data.datasets[1].data = fixedBugs;
        }

        // 如果图表有第三个数据集（BUG总数），则更新它
        if (window.bugTrendChart.data.datasets.length > 2 && window.bugTrendChart.data.datasets[2]) {
            window.bugTrendChart.data.datasets[2].data = totalBugs;
        }

        window.bugTrendChart.update('none');
        console.log('BUG趋势图表更新成功');
    } catch (error) {
        console.error('更新BUG趋势图表失败:', error);
    }
}

function switchTrendChart(unit, chartType) {
    console.log('切换趋势图表:', unit, chartType);

    try {
        // 安全销毁现有图表
        const existingChart = Chart.getChart('bugTrendChart');
        if (existingChart) {
            console.log('销毁现有BUG趋势图表');
            existingChart.destroy();
        }

        if (window.bugTrendChart && typeof window.bugTrendChart.destroy === 'function') {
            window.bugTrendChart.destroy();
        }
        window.bugTrendChart = null;

        // 获取画布元素
        const bugTrendCtx = document.getElementById('bugTrendChart');
        if (!bugTrendCtx) {
            console.error('找不到BUG趋势图表画布元素');
            return;
        }

        // 清理画布上下文
        const ctx = bugTrendCtx.getContext('2d');
        ctx.clearRect(0, 0, bugTrendCtx.width, bugTrendCtx.height);
        window.bugTrendChart = new Chart(ctx, {
        type: chartType,
        data: {
            labels: [],
            datasets: [{
                label: '新增BUG',
                data: [],
                borderColor: '#dc3545',
                backgroundColor: chartType === 'bar' ? 'rgba(220, 53, 69, 0.8)' : 'rgba(220, 53, 69, 0.1)',
                tension: chartType === 'line' ? 0.4 : 0,
                fill: chartType === 'line',
                pointBackgroundColor: '#dc3545',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: chartType === 'line' ? 4 : 0,
                pointHoverRadius: chartType === 'line' ? 6 : 0
            }, {
                label: '修复BUG',
                data: [],
                borderColor: '#28a745',
                backgroundColor: chartType === 'bar' ? 'rgba(40, 167, 69, 0.8)' : 'rgba(40, 167, 69, 0.1)',
                tension: chartType === 'line' ? 0.4 : 0,
                fill: chartType === 'line',
                pointBackgroundColor: '#28a745',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: chartType === 'line' ? 4 : 0,
                pointHoverRadius: chartType === 'line' ? 6 : 0
            }, {
                label: 'BUG总数',
                data: [],
                borderColor: '#ffc107',
                backgroundColor: chartType === 'bar' ? 'rgba(255, 193, 7, 0.8)' : 'rgba(255, 193, 7, 0.1)',
                tension: chartType === 'line' ? 0.4 : 0,
                fill: false,
                pointBackgroundColor: '#ffc107',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: chartType === 'line' ? 4 : 0,
                pointHoverRadius: chartType === 'line' ? 6 : 0,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1  // 确保Y轴显示整数
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    title: {
                        display: true,
                        text: '新增/修复数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1  // 确保Y轴显示整数
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'BUG总数'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return '日期: ' + context[0].label;
                        },
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y}`;
                        }
                    }
                }
            },
            onHover: function(event, elements) {
                event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const date = this.data.labels[index];
                    showBugTrendDetails(date, index);
                }
            }
        }
    });

        // 加载新数据
        loadBugTrendData(unit, chartType);
        console.log('BUG趋势图表切换成功');
    } catch (error) {
        console.error('切换BUG趋势图表失败:', error);
        // 如果创建失败，尝试恢复默认图表
        if (!window.bugTrendChart) {
            console.log('尝试恢复默认BUG趋势图表');
            initBugTrendChart();
        }
    }
}

function initBugTrendChart() {
    // 恢复默认BUG趋势图表
    try {
        // 先销毁现有图表
        const existingChart = Chart.getChart('bugTrendChart');
        if (existingChart) {
            existingChart.destroy();
        }

        if (window.bugTrendChart && typeof window.bugTrendChart.destroy === 'function') {
            window.bugTrendChart.destroy();
        }
        window.bugTrendChart = null;

        const bugTrendCtx = document.getElementById('bugTrendChart');
        if (!bugTrendCtx) {
            console.error('找不到BUG趋势图表画布元素');
            return;
        }

        const ctx = bugTrendCtx.getContext('2d');
        ctx.clearRect(0, 0, bugTrendCtx.width, bugTrendCtx.height);

        window.bugTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '新增BUG',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '修复BUG',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'BUG总数',
                    data: [],
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4,
                    fill: false,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { stepSize: 1 }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        ticks: { stepSize: 1 },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });

        console.log('默认BUG趋势图表恢复成功');
    } catch (error) {
        console.error('恢复默认BUG趋势图表失败:', error);
    }
}

function loadBugTrendData(unit = 'day', chartType = 'line') {
    $.get('/api/dashboard/bug_trend', {
        days: unit === 'week' ? 28 : 30,  // 周视图显示4周，日视图显示30天
        unit: unit
    })
    .done(function(data) {
        updateBugTrendChart(data);
        $('#bug-trend-update').text('更新于 ' + new Date().toLocaleTimeString());
    })
    .fail(function() {
        console.error('获取BUG趋势数据失败');
    });
}

function updateConnectionStatus(online) {
    const indicator = $('#connection-status');
    if (online) {
        indicator.removeClass('status-offline').addClass('status-online');
    } else {
        indicator.removeClass('status-online').addClass('status-offline');
    }
}

function showRefreshIndicator() {
    $('.refresh-indicator').show();
}

function hideRefreshIndicator() {
    $('.refresh-indicator').hide();
}

function refreshDashboard() {
    if (window.realtimeManager) {
        window.realtimeManager.forceRefresh();
    } else {
        loadDashboardData();
    }
}

function updateConnectionInfo(isConnected) {
    try {
        if (window.realtimeManager) {
            const status = window.realtimeManager.getConnectionStatus();
            console.log('连接状态信息:', status);

            // 更新连接文本
            const refreshRate = Math.round(status.currentRefreshRate / 1000);
            const connectionText = document.getElementById('connection-text');

            if (connectionText) {
                if (isConnected && status.isWebSocketSupported) {
                    connectionText.textContent = '实时连接';
                    document.title = '🟢 RunSim 仪表板 (实时连接)';
                } else {
                    connectionText.textContent = `${refreshRate}s刷新`;
                    document.title = `🔄 RunSim 仪表板 (${refreshRate}s刷新)`;
                }
            }

            // 更新性能面板
            updatePerformancePanel(status);
        }
    } catch (error) {
        console.error('更新连接信息失败:', error);
    }
}

// 性能监控功能
function togglePerformancePanel() {
    const panel = document.getElementById('performance-panel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        updatePerformancePanel();
        // 启动性能监控定时器
        if (!window.performanceTimer) {
            window.performanceTimer = setInterval(updatePerformancePanel, 2000);
        }
    } else {
        panel.style.display = 'none';
        // 停止性能监控定时器
        if (window.performanceTimer) {
            clearInterval(window.performanceTimer);
            window.performanceTimer = null;
        }
    }
}

function updatePerformancePanel(status) {
    try {
        if (!status && window.realtimeManager) {
            status = window.realtimeManager.getConnectionStatus();
        }

        if (status) {
            // 连接状态
            const connectionStatus = status.isConnected ?
                (status.isWebSocketSupported ? '🟢 WebSocket' : '🟡 轮询') :
                '🔴 离线';

            const perfConnectionStatus = document.getElementById('perf-connection-status');
            if (perfConnectionStatus) {
                perfConnectionStatus.textContent = connectionStatus;
            }

            // 刷新频率
            const refreshRate = Math.round(status.currentRefreshRate / 1000);
            const perfRefreshRate = document.getElementById('perf-refresh-rate');
            if (perfRefreshRate) {
                perfRefreshRate.textContent = `${refreshRate}s`;
            }

            // 最后更新时间
            const lastUpdate = new Date(status.lastUpdateTime).toLocaleTimeString();
            const perfLastUpdate = document.getElementById('perf-last-update');
            if (perfLastUpdate) {
                perfLastUpdate.textContent = lastUpdate;
            }

            // 重试次数
            const perfRetryCount = document.getElementById('perf-retry-count');
            if (perfRetryCount) {
                perfRetryCount.textContent = status.retryCount;
            }
        }

        // 页面性能信息
        updatePagePerformance();
    } catch (error) {
        console.error('更新性能面板失败:', error);
    }
}

function updatePagePerformance() {
    try {
        // 内存使用情况（如果支持）
        if (performance.memory) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

            const percentage = Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100);

            const memoryBar = document.getElementById('perf-memory-bar');
            const memoryText = document.getElementById('perf-memory-text');

            if (memoryBar) {
                memoryBar.style.width = `${percentage}%`;

                // 根据内存使用情况调整进度条颜色
                memoryBar.className = 'progress-bar';
                if (percentage > 80) {
                    memoryBar.classList.add('bg-danger');
                } else if (percentage > 60) {
                    memoryBar.classList.add('bg-warning');
                } else {
                    memoryBar.classList.add('bg-info');
                }
            }

            if (memoryText) {
                memoryText.textContent = `内存使用: ${usedMB}MB / ${limitMB}MB (${percentage}%)`;
            }
        } else {
            const memoryText = document.getElementById('perf-memory-text');
            if (memoryText) {
                memoryText.textContent = '内存信息不可用';
            }
        }

        // 页面加载性能（如果支持）
        if (performance.timing) {
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            if (loadTime > 0) {
                console.log(`页面加载时间: ${loadTime}ms`);
            }
        }

    } catch (error) {
        console.warn('获取性能信息失败:', error);
        const memoryText = document.getElementById('perf-memory-text');
        if (memoryText) {
            memoryText.textContent = '性能信息获取失败';
        }
    }
}

// 图表交互功能
function showStatusDetails(label, index) {
    // 数据验证
    if (!window.statusChart || !window.statusChart.data || !window.statusChart.data.datasets || !window.statusChart.data.datasets[0]) {
        console.warn('状态图表数据不可用');
        return;
    }

    const data = window.statusChart.data.datasets[0].data;
    if (!data || index >= data.length) {
        console.warn('状态图表数据索引无效');
        return;
    }

    const total = data.reduce((a, b) => a + b, 0);
    const value = data[index];
    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;

    showModal('用例状态详情', `
        <div class="text-center">
            <h4 class="text-primary">${label}</h4>
            <p class="h2 mb-3">${value}</p>
            <p class="text-muted">占总数的 ${percentage}%</p>
            <hr>
            <a href="/testplan?status=${encodeURIComponent(label)}" class="btn btn-primary">
                <i class="fas fa-list me-1"></i>查看详细用例
            </a>
        </div>
    `);
}

function showProgressDetails(label, value) {
    showModal('项目进度详情', `
        <div class="text-center">
            <h4 class="text-info">${label}</h4>
            <div class="progress mb-3" style="height: 20px;">
                <div class="progress-bar" role="progressbar" style="width: ${value}%">${value}%</div>
            </div>
            <p class="text-muted">当前完成进度</p>
            <hr>
            <a href="/testplan?stage=${encodeURIComponent(label)}" class="btn btn-info">
                <i class="fas fa-tasks me-1"></i>查看相关用例
            </a>
        </div>
    `);
}

function showBugTrendDetails(date, index) {
    // 数据验证
    if (!window.bugTrendChart || !window.bugTrendChart.data || !window.bugTrendChart.data.datasets) {
        console.warn('BUG趋势图表数据不可用');
        return;
    }

    const datasets = window.bugTrendChart.data.datasets;
    if (datasets.length < 2) {
        console.warn('BUG趋势图表数据集不完整');
        return;
    }

    const newBugs = (datasets[0] && datasets[0].data && datasets[0].data[index]) || 0;
    const fixedBugs = (datasets[1] && datasets[1].data && datasets[1].data[index]) || 0;
    const totalBugs = (datasets.length > 2 && datasets[2] && datasets[2].data && datasets[2].data[index]) || 0;

    showModal('BUG趋势详情', `
        <div class="text-center">
            <h4 class="text-warning">${date}</h4>
            <div class="row">
                <div class="col-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <h5>新增BUG</h5>
                            <h2>${newBugs}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5>修复BUG</h5>
                            <h2>${fixedBugs}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <h5>BUG总数</h5>
                            <h2>${totalBugs}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <p class="text-muted">
                    净变化: ${newBugs - fixedBugs > 0 ? '+' : ''}${newBugs - fixedBugs}
                </p>
            </div>
            <hr>
            <a href="/bug?date=${date}" class="btn btn-warning">
                <i class="fas fa-bug me-1"></i>查看当日BUG
            </a>
        </div>
    `);
}

function showModal(title, content) {
    // 创建模态框
    const modalHtml = `
        <div class="modal fade" id="detailModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#detailModal').remove();

    // 添加新模态框并显示
    $('body').append(modalHtml);
    $('#detailModal').modal('show');

    // 模态框关闭后移除
    $('#detailModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

// 数据导出功能
function exportReport() {
    showExportModal();
}

function showExportModal() {
    const modalContent = `
        <div class="row g-3">
            <div class="col-12">
                <h6>选择导出格式：</h6>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-success w-100" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>Excel报告
                </button>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-danger w-100" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>PDF报告
                </button>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-info w-100" onclick="exportToJSON()">
                    <i class="fas fa-file-code me-2"></i>JSON数据
                </button>
            </div>
            <div class="col-md-6">
                <button class="btn btn-outline-secondary w-100" onclick="exportChartImages()">
                    <i class="fas fa-image me-2"></i>图表图片
                </button>
            </div>
            <div class="col-12 mt-3">
                <h6>导出选项：</h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                    <label class="form-check-label" for="includeCharts">包含图表</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="includeRawData" checked>
                    <label class="form-check-label" for="includeRawData">包含原始数据</label>
                </div>
            </div>
        </div>
    `;

    showModal('导出报告', modalContent);
}

async function exportToExcel() {
    try {
        showLoadingMessage('正在生成Excel报告...');

        const response = await fetch('/api/dashboard/export/excel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                includeCharts: $('#includeCharts').is(':checked'),
                includeRawData: $('#includeRawData').is(':checked')
            })
        });

        if (response.ok) {
            const blob = await response.blob();
            downloadFile(blob, 'dashboard_report.xlsx');
            hideLoadingMessage();
            showSuccessMessage('Excel报告导出成功！');
        } else {
            throw new Error('导出失败');
        }
    } catch (error) {
        hideLoadingMessage();
        showErrorMessage('Excel报告导出失败: ' + error.message);
    }

    $('#detailModal').modal('hide');
}

async function exportToPDF() {
    try {
        showLoadingMessage('正在生成PDF报告...');

        const response = await fetch('/api/dashboard/export/pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                includeCharts: $('#includeCharts').is(':checked'),
                includeRawData: $('#includeRawData').is(':checked')
            })
        });

        if (response.ok) {
            const blob = await response.blob();
            downloadFile(blob, 'dashboard_report.pdf');
            hideLoadingMessage();
            showSuccessMessage('PDF报告导出成功！');
        } else {
            throw new Error('导出失败');
        }
    } catch (error) {
        hideLoadingMessage();
        showErrorMessage('PDF报告导出失败: ' + error.message);
    }

    $('#detailModal').modal('hide');
}

async function exportToJSON() {
    try {
        showLoadingMessage('正在导出JSON数据...');

        const response = await fetch('/api/dashboard/export/json');

        if (response.ok) {
            const data = await response.json();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            downloadFile(blob, 'dashboard_data.json');
            hideLoadingMessage();
            showSuccessMessage('JSON数据导出成功！');
        } else {
            throw new Error('导出失败');
        }
    } catch (error) {
        hideLoadingMessage();
        showErrorMessage('JSON数据导出失败: ' + error.message);
    }

    $('#detailModal').modal('hide');
}

function exportChartImages() {
    try {
        // 导出状态图表
        const statusCanvas = document.getElementById('statusChart');
        downloadCanvasAsImage(statusCanvas, 'status_chart.png');

        // 导出进度图表
        const progressCanvas = document.getElementById('progressChart');
        downloadCanvasAsImage(progressCanvas, 'progress_chart.png');

        // 导出趋势图表
        const trendCanvas = document.getElementById('bugTrendChart');
        downloadCanvasAsImage(trendCanvas, 'bug_trend_chart.png');

        showSuccessMessage('图表图片导出成功！');
    } catch (error) {
        showErrorMessage('图表图片导出失败: ' + error.message);
    }

    $('#detailModal').modal('hide');
}

// 工具函数
function downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

function downloadCanvasAsImage(canvas, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png');
    link.click();
}

function showLoadingMessage(message) {
    const toast = `
        <div class="toast-container position-fixed top-0 end-0 p-3">
            <div id="loadingToast" class="toast show" role="alert">
                <div class="toast-body">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        ${message}
                    </div>
                </div>
            </div>
        </div>
    `;
    $('body').append(toast);
}

function hideLoadingMessage() {
    $('#loadingToast').closest('.toast-container').remove();
}

function showSuccessMessage(message) {
    showToast(message, 'success');
}

function showErrorMessage(message) {
    showToast(message, 'danger');
}

function showToast(message, type) {
    const toast = `
        <div class="toast-container position-fixed top-0 end-0 p-3">
            <div class="toast show" role="alert" data-bs-autohide="true" data-bs-delay="3000">
                <div class="toast-body bg-${type} text-white">
                    ${message}
                </div>
            </div>
        </div>
    `;
    $('body').append(toast);

    setTimeout(() => {
        $('.toast-container').last().remove();
    }, 3500);
}

// 验证阶段相关函数
function loadPhaseData() {
    // 加载阶段分布数据
    $.get('/api/dashboard/phase_distribution')
        .done(function(data) {
            updatePhaseStatistics(data);
            $('#phase-update').text('更新于 ' + new Date().toLocaleTimeString());
        })
        .fail(function() {
            console.error('加载验证阶段数据失败');
        });

    // 加载阶段进度数据
    $.get('/api/dashboard/phase_progress')
        .done(function(data) {
            updatePhaseProgress(data);
        })
        .fail(function() {
            console.error('加载验证阶段进度失败');
        });
}

function updatePhaseProgress(data) {
    console.log('更新验证阶段进度数据:', data);
    const container = $('#phase-progress-container');
    container.empty();

    const phases = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2'];
    const phaseColors = {
        'DVR1': '#007bff',
        'DVR2': '#28a745',
        'DVR3': '#ffc107',
        'DVS1': '#17a2b8',
        'DVS2': '#6f42c1'
    };

    // 检查数据格式
    const progressData = data.phase_progress || data.phases || {};
    console.log('阶段进度数据:', progressData);

    phases.forEach(phase => {
        const phaseData = progressData[phase] || {};
        console.log(`${phase} 阶段数据:`, phaseData);

        // 兼容不同的数据格式
        const progress = phaseData.progress || phaseData.progress_percentage || 0;
        const totalCases = phaseData.total || phaseData.total_cases || 0;
        const completedCases = phaseData.passed || phaseData.completed_cases || 0;

        const progressHtml = `
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="fw-bold">${phase}</span>
                    <span class="text-muted">${completedCases}/${totalCases} (${progress}%)</span>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar"
                         style="width: ${progress}%; background-color: ${phaseColors[phase]};"
                         role="progressbar"
                         aria-valuenow="${progress}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                    </div>
                </div>
            </div>
        `;
        container.append(progressHtml);
    });

    // 如果没有数据，显示提示信息
    if (container.children().length === 0) {
        container.append(`
            <div class="text-center text-muted py-3">
                <i class="fas fa-info-circle mb-2"></i>
                <div>暂无验证阶段进度数据</div>
            </div>
        `);
    }
}

function updatePhaseStatistics(data) {
    console.log('更新验证阶段统计数据:', data);
    const tbody = $('#phase-statistics-table tbody');
    tbody.empty();

    const phases = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2'];
    const phaseDistribution = data.phase_distribution || data.phases || {};
    console.log('阶段分布数据:', phaseDistribution);

    phases.forEach(phase => {
        const phaseData = phaseDistribution[phase] || {};
        console.log(`${phase} 阶段统计数据:`, phaseData);

        // 计算各类型用例统计 - 兼容不同的数据格式
        let subsysStats = phaseData.subsys || {total: 0, pass: 0, pending: 0, ongoing: 0, na: 0};
        let topStats = phaseData.top || {total: 0, pass: 0, pending: 0, ongoing: 0, na: 0};
        let postSubsysStats = phaseData.post_subsys || {total: 0, pass: 0, pending: 0, ongoing: 0, na: 0};
        let postTopStats = phaseData.post_top || {total: 0, pass: 0, pending: 0, ongoing: 0, na: 0};

        // 如果数据格式不同，尝试从其他字段获取
        if (phaseData.total !== undefined) {
            // 如果是简化格式，使用总体数据
            const totalStats = {
                total: phaseData.total || 0,
                pass: phaseData.passed || 0,
                pending: phaseData.pending || 0,
                ongoing: phaseData.running || 0,
                na: 0
            };
            subsysStats = totalStats;
            topStats = totalStats;
        }

        // 计算总计
        let totalCases = (subsysStats.total || 0) + (topStats.total || 0) +
                        (postSubsysStats.total || 0) + (postTopStats.total || 0);
        let totalCompleted = (subsysStats.pass || 0) + (topStats.pass || 0) +
                           (postSubsysStats.pass || 0) + (postTopStats.pass || 0);

        let progressPercentage = totalCases > 0 ? Math.round((totalCompleted / totalCases) * 100) : 0;

        const row = `
            <tr>
                <td class="fw-bold">${phase}</td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="badge bg-primary mb-1">总计: ${subsysStats.total || 0}</span>
                        <div class="small">
                            <span class="text-success me-2">✓${subsysStats.pass || 0}</span>
                            <span class="text-info me-2">⏳${subsysStats.pending || 0}</span>
                            <span class="text-warning me-2">⚡${subsysStats.ongoing || 0}</span>
                            <span class="text-muted">N/A${subsysStats.na || 0}</span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="badge bg-success mb-1">总计: ${topStats.total || 0}</span>
                        <div class="small">
                            <span class="text-success me-2">✓${topStats.pass || 0}</span>
                            <span class="text-info me-2">⏳${topStats.pending || 0}</span>
                            <span class="text-warning me-2">⚡${topStats.ongoing || 0}</span>
                            <span class="text-muted">N/A${topStats.na || 0}</span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="badge bg-warning mb-1">总计: ${postSubsysStats.total || 0}</span>
                        <div class="small">
                            <span class="text-success me-2">✓${postSubsysStats.pass || 0}</span>
                            <span class="text-info me-2">⏳${postSubsysStats.pending || 0}</span>
                            <span class="text-warning me-2">⚡${postSubsysStats.ongoing || 0}</span>
                            <span class="text-muted">N/A${postSubsysStats.na || 0}</span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="badge bg-info mb-1">总计: ${postTopStats.total || 0}</span>
                        <div class="small">
                            <span class="text-success me-2">✓${postTopStats.pass || 0}</span>
                            <span class="text-info me-2">⏳${postTopStats.pending || 0}</span>
                            <span class="text-warning me-2">⚡${postTopStats.ongoing || 0}</span>
                            <span class="text-muted">N/A${postTopStats.na || 0}</span>
                        </div>
                    </div>
                </td>
                <td class="fw-bold text-center">
                    <span class="h5 text-primary">${totalCases}</span>
                    <div class="small text-muted">用例总数</div>
                </td>
                <td>
                    <div class="progress" style="height: 25px; min-width: 100px;">
                        <div class="progress-bar bg-success"
                             style="width: ${progressPercentage}%;"
                             role="progressbar"
                             aria-valuenow="${progressPercentage}"
                             aria-valuemin="0"
                             aria-valuemax="100">
                            ${progressPercentage}%
                        </div>
                    </div>
                    <div class="small text-center mt-1 text-muted">
                        ${totalCompleted}/${totalCases} 完成
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary"
                                onclick="showPhaseDetails('${phase}')"
                                title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-info"
                                onclick="exportPhaseData('${phase}')"
                                title="导出数据">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });

    // 如果没有数据，显示提示信息
    if (tbody.children().length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle mb-2"></i>
                    <div>暂无验证阶段统计数据</div>
                </td>
            </tr>
        `);
    }
}

function refreshPhaseData() {
    showRefreshIndicator();
    loadPhaseData();
    hideRefreshIndicator();
}

function showPhaseDetails(phase) {
    // 显示阶段详情模态框
    const modalHtml = `
        <div class="modal fade" id="phaseDetailModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${phase} 阶段详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Subsys级用例</h6>
                                <div id="subsys-cases-${phase}">加载中...</div>
                            </div>
                            <div class="col-md-6">
                                <h6>TOP级用例</h6>
                                <div id="top-cases-${phase}">加载中...</div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6>POST_Subsys用例</h6>
                                <div id="post-subsys-cases-${phase}">加载中...</div>
                            </div>
                            <div class="col-md-6">
                                <h6>POST_TOP用例</h6>
                                <div id="post-top-cases-${phase}">加载中...</div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="exportPhaseData('${phase}')">导出数据</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#phaseDetailModal').remove();
    $('body').append(modalHtml);

    // 显示模态框
    $('#phaseDetailModal').modal('show');

    // 加载各类型用例数据
    const caseTypes = ['subsys', 'top', 'post_subsys', 'post_top'];
    caseTypes.forEach(caseType => {
        $.get('/api/dashboard/phase_cases', {
            phase: phase,
            case_type: caseType
        })
        .done(function(data) {
            const container = $(`#${caseType.replace('_', '-')}-cases-${phase}`);
            if (data.cases && data.cases.length > 0) {
                let html = '<div class="list-group list-group-flush">';
                data.cases.slice(0, 10).forEach(testCase => {
                    const statusClass = testCase.status === 'PASS' ? 'success' :
                                       testCase.status === 'Pending' ? 'secondary' :
                                       testCase.status === 'On-Going' ? 'warning' :
                                       testCase.status === 'N/A' ? 'light' : 'secondary';
                    html += `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>${testCase.case_name}</span>
                            <span class="badge bg-${statusClass}">${testCase.status || 'Pending'}</span>
                        </div>
                    `;
                });
                if (data.cases.length > 10) {
                    html += `<div class="list-group-item text-center text-muted">还有 ${data.cases.length - 10} 个用例...</div>`;
                }
                html += '</div>';
                container.html(html);
            } else {
                container.html('<p class="text-muted">暂无用例</p>');
            }
        })
        .fail(function() {
            const container = $(`#${caseType.replace('_', '-')}-cases-${phase}`);
            container.html('<p class="text-danger">加载失败</p>');
        });
    });
}

function exportPhaseData(phase) {
    // 导出指定阶段的数据
    window.open(`/api/dashboard/phase_cases?phase=${phase}&case_type=all&format=excel`, '_blank');
}
</script>
{% endblock %}
