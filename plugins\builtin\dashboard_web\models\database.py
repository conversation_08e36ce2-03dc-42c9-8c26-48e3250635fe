"""
数据库管理模块

该模块负责数据库的初始化、连接管理和基础操作。
使用SQLite作为本地数据库，支持WAL模式以提高并发性能。
"""

import sqlite3
import os
import logging
from contextlib import contextmanager
from datetime import datetime
from typing import Optional, Dict, List, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._ensure_db_directory()

    def _ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            logger.info(f"创建数据库目录: {db_dir}")

    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器

        Yields:
            sqlite3.Connection: 数据库连接对象
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问

            # 启用WAL模式以提高并发性能
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=1000")
            conn.execute("PRAGMA temp_store=MEMORY")

            yield conn

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()

def init_database(db_path: str) -> bool:
    """
    初始化数据库，创建所有必要的表

    Args:
        db_path: 数据库文件路径

    Returns:
        bool: 初始化是否成功
    """
    try:
        db_manager = DatabaseManager(db_path)

        with db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 创建项目表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    subsystem TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建用例表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_cases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    category TEXT,
                    number TEXT,
                    test_areas TEXT,
                    test_scope TEXT,
                    function_point TEXT,
                    check_point TEXT,
                    cover TEXT,
                    case_name TEXT NOT NULL,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    actual_time INTEGER,
                    owner TEXT,
                    -- 验证阶段字段 (DVR1/DVR2/DVR3/DVS1/DVS2 或 N/A)
                    subsys_phase TEXT,
                    subsys_status TEXT DEFAULT 'Not Started',
                    top_phase TEXT,
                    top_status TEXT DEFAULT 'Not Started',
                    -- 后仿标记字段 (√ 或空白)
                    post_subsys_phase TEXT,
                    post_subsys_status TEXT DEFAULT 'Not Started',
                    post_top_phase TEXT,
                    post_top_status TEXT DEFAULT 'Not Started',
                    remarks TEXT,
                    -- 兼容旧格式字段
                    subsys_stage TEXT,  -- 保持向后兼容
                    top_stage TEXT,     -- 保持向后兼容
                    test_process TEXT,
                    coverage_point TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
                )
            ''')

            # 创建BUG表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS bugs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    bug_id TEXT NOT NULL,
                    bug_type TEXT,
                    submit_sys TEXT,
                    verification_stage TEXT,
                    description TEXT,
                    discovery_platform TEXT,
                    discovery_case TEXT,
                    severity TEXT DEFAULT 'Medium',
                    status TEXT DEFAULT 'Open',
                    submitter TEXT,
                    verifier TEXT,
                    submit_date DATE,
                    fix_date DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
                )
            ''')

            # 创建用例状态历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS case_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER,
                    old_status TEXT,
                    new_status TEXT,
                    stage_type TEXT,  -- 'subsys', 'top', 'post_subsys', 'post_top'
                    changed_by TEXT,
                    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (case_id) REFERENCES test_cases (id) ON DELETE CASCADE
                )
            ''')

            # 创建系统配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT NOT NULL UNIQUE,
                    config_value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建验证阶段统计表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS phase_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    phase_name TEXT NOT NULL,  -- DVR1/DVR2/DVR3/DVS1/DVS2
                    case_type TEXT NOT NULL,   -- subsys/top/post_subsys/post_top
                    total_cases INTEGER DEFAULT 0,
                    pass_cases INTEGER DEFAULT 0,
                    fail_cases INTEGER DEFAULT 0,
                    ongoing_cases INTEGER DEFAULT 0,
                    not_started_cases INTEGER DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
                    UNIQUE(project_id, phase_name, case_type)
                )
            ''')

            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_name ON test_cases(case_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_project ON test_cases(project_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_bugs_project ON bugs(project_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_bugs_status ON bugs(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_case_history_case ON case_status_history(case_id)')

            # 插入默认配置
            default_configs = [
                ('dashboard_refresh_interval', '30', '仪表板数据刷新间隔（秒）'),
                ('default_project', '', '默认项目名称'),
                ('max_upload_size', '16777216', '最大上传文件大小（字节）'),
            ]

            for key, value, desc in default_configs:
                cursor.execute('''
                    INSERT OR IGNORE INTO system_config (config_key, config_value, description)
                    VALUES (?, ?, ?)
                ''', (key, value, desc))

            # 插入示例项目（如果不存在）
            cursor.execute('''
                INSERT OR IGNORE INTO projects (name, subsystem, description)
                VALUES (?, ?, ?)
            ''', ('默认项目', 'default', '系统默认项目'))

            conn.commit()
            logger.info(f"数据库初始化成功: {db_path}")
            return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False

def get_db_manager(db_path: str) -> DatabaseManager:
    """
    获取数据库管理器实例

    Args:
        db_path: 数据库文件路径

    Returns:
        DatabaseManager: 数据库管理器实例
    """
    return DatabaseManager(db_path)

# Flask应用中使用的便捷函数
def get_db():
    """
    Flask应用中获取数据库连接的便捷函数
    需要在Flask应用上下文中使用
    """
    try:
        from flask import current_app
        db_path = current_app.config['DATABASE_PATH']
        db_manager = DatabaseManager(db_path)
        return db_manager.get_connection()
    except Exception as e:
        logger.error(f"获取数据库连接失败: {str(e)}")
        raise
