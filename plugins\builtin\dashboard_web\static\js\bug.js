/**
 * BUG管理页面JavaScript功能
 *
 * 提供BUG管理相关的前端交互功能，包括：
 * - BUG列表加载和分页
 * - BUG的增删改查操作
 * - 统计图表展示
 * - 过滤和搜索功能
 */

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalPages = 0;
let bugStatusChart = null;
let bugSeverityChart = null;
let bugTrendChart = null;

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('BUG管理页面JavaScript开始加载...');

    try {
        initializePage();
        loadBugStatistics();
        loadBugList();
        initializeCharts();
        setupEventListeners();

        console.log('BUG管理页面JavaScript加载完成');
    } catch (error) {
        console.error('BUG管理页面JavaScript加载失败:', error);
    }
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置默认日期
    const today = new Date().toISOString().split('T')[0];
    $('#submitDate').val(today);

    console.log('BUG管理页面初始化完成');

    // 添加测试按钮（仅用于调试）
    if (window.location.search.includes('debug=1')) {
        addDebugButton();
    }
}

/**
 * 添加调试按钮
 */
function addDebugButton() {
    const debugButton = $(`
        <button type="button" class="btn btn-warning btn-sm ms-2" onclick="testBugCreation()">
            测试创建BUG
        </button>
    `);
    $('.card-header h5:contains("BUG列表")').parent().find('.btn-group').append(debugButton);
}

/**
 * 测试BUG创建功能
 */
function testBugCreation() {
    console.log('开始测试BUG创建...');

    const testData = {
        bug_id: 'DEBUG-TEST-' + Date.now(),
        description: '调试测试BUG - ' + new Date().toLocaleString(),
        severity: 'Medium',
        status: 'Open',
        submitter: '调试用户'
    };

    console.log('测试数据:', testData);

    $.ajax({
        url: '/api/bugs',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testData),
        success: function(response) {
            console.log('测试成功:', response);
            alert('测试创建BUG成功！ID: ' + response.data.id);
            loadBugList();
            loadBugStatistics();
        },
        error: function(xhr, status, error) {
            console.error('测试失败:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });
            alert('测试创建BUG失败: ' + (xhr.responseText || error));
        }
    });
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 过滤器变化事件
    $('#statusFilter, #severityFilter').change(function() {
        currentPage = 1;
        loadBugList();
    });

    // 搜索输入事件（防抖）
    let searchTimeout;
    $('#searchInput').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            currentPage = 1;
            loadBugList();
        }, 500);
    });

    // 模态框关闭时重置表单
    $('#addBugModal').on('hidden.bs.modal', function() {
        $('#addBugForm')[0].reset();
        const today = new Date().toISOString().split('T')[0];
        $('#submitDate').val(today);
    });

    $('#editBugModal').on('hidden.bs.modal', function() {
        $('#editBugForm')[0].reset();
    });
}

/**
 * 加载BUG统计信息
 */
function loadBugStatistics() {
    $.ajax({
        url: '/api/bugs/statistics',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateStatisticsCards(response.data);
                updateStatusChart(response.data.status_distribution);
                updateSeverityChart(response.data.severity_distribution);
            } else {
                console.error('获取BUG统计失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取BUG统计失败:', error);
            showErrorMessage('获取统计信息失败');
        }
    });
}

/**
 * 更新统计卡片
 */
function updateStatisticsCards(stats) {
    $('#total-bugs').text(stats.total_bugs || 0);
    $('#open-bugs').text(stats.open_bugs || 0);
    $('#fixed-bugs').text(stats.fixed_bugs || 0);
    $('#closed-bugs').text(stats.closed_bugs || 0);
}

/**
 * 加载BUG列表
 */
function loadBugList() {
    const params = {
        page: currentPage,
        page_size: pageSize,
        status: $('#statusFilter').val(),
        severity: $('#severityFilter').val(),
        search: $('#searchInput').val()
    };

    // 显示加载状态
    $('#bugTableBody').html(`
        <tr>
            <td colspan="8" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载BUG数据...</p>
            </td>
        </tr>
    `);

    $.ajax({
        url: '/api/bugs',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                renderBugTable(response.data.bugs);
                updatePagination(response.data.pagination);
            } else {
                showErrorMessage('获取BUG列表失败: ' + response.message);
                renderEmptyTable();
            }
        },
        error: function(xhr, status, error) {
            console.error('获取BUG列表失败:', error);
            showErrorMessage('获取BUG列表失败');
            renderEmptyTable();
        }
    });
}

/**
 * 渲染BUG表格
 */
function renderBugTable(bugs) {
    if (!bugs || bugs.length === 0) {
        renderEmptyTable();
        return;
    }

    const tbody = $('#bugTableBody');
    tbody.empty();

    bugs.forEach(bug => {
        const row = $(`
            <tr>
                <td><strong>${escapeHtml(bug.bug_id)}</strong></td>
                <td>${escapeHtml(bug.bug_type || '')}</td>
                <td><span class="badge bg-${getSeverityColor(bug.severity)}">${escapeHtml(bug.severity || '')}</span></td>
                <td><span class="badge bg-${getStatusColor(bug.status)}">${escapeHtml(bug.status || '')}</span></td>
                <td class="text-truncate" style="max-width: 200px;" title="${escapeHtml(bug.description || '')}">${escapeHtml(bug.description || '')}</td>
                <td>${escapeHtml(bug.submitter || '')}</td>
                <td>${formatDate(bug.submit_date)}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="editBug(${bug.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteBug(${bug.id}, '${escapeHtml(bug.bug_id)}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

/**
 * 渲染空表格
 */
function renderEmptyTable() {
    $('#bugTableBody').html(`
        <tr>
            <td colspan="8" class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无BUG数据</p>
            </td>
        </tr>
    `);
}

/**
 * 更新分页
 */
function updatePagination(pagination) {
    totalPages = pagination.total_pages;
    currentPage = pagination.page;

    if (totalPages <= 1) {
        $('#paginationContainer').hide();
        return;
    }

    $('#paginationContainer').show();
    const paginationEl = $('#pagination');
    paginationEl.empty();

    // 上一页
    const prevDisabled = !pagination.has_prev ? 'disabled' : '';
    paginationEl.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `);

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        paginationEl.append(`<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`);
        if (startPage > 2) {
            paginationEl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const active = i === currentPage ? 'active' : '';
        paginationEl.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationEl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        paginationEl.append(`<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`);
    }

    // 下一页
    const nextDisabled = !pagination.has_next ? 'disabled' : '';
    paginationEl.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `);
}

/**
 * 切换页面
 */
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }
    currentPage = page;
    loadBugList();
}

/**
 * 保存BUG
 */
function saveBug() {
    const form = $('#addBugForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const formData = new FormData(form);
    const bugData = {};

    for (let [key, value] of formData.entries()) {
        // 处理空值 - 将空字符串转换为null或合适的默认值
        if (value === '') {
            if (key === 'submit_date' || key === 'fix_date') {
                bugData[key] = null;
            } else {
                bugData[key] = value; // 保持空字符串用于文本字段
            }
        } else {
            bugData[key] = value;
        }
    }

    // 调试信息
    console.log('准备提交的BUG数据:', bugData);

    $.ajax({
        url: '/api/bugs',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(bugData),
        success: function(response) {
            console.log('服务器响应:', response);
            if (response.success) {
                $('#addBugModal').modal('hide');
                showSuccessMessage('BUG创建成功');
                loadBugList();
                loadBugStatistics();
            } else {
                console.error('创建失败，服务器消息:', response.message);
                showErrorMessage('创建BUG失败: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX请求失败:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });

            // 尝试解析错误响应
            let errorMessage = '创建BUG失败';
            try {
                const errorResponse = JSON.parse(xhr.responseText);
                if (errorResponse.message) {
                    errorMessage = '创建BUG失败: ' + errorResponse.message;
                }
            } catch (e) {
                // 如果无法解析JSON，使用默认错误消息
                errorMessage = '创建BUG失败: ' + (xhr.responseText || error);
            }

            showErrorMessage(errorMessage);
        }
    });
}

/**
 * 编辑BUG
 */
function editBug(bugId) {
    $.ajax({
        url: `/api/bugs/${bugId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                populateEditForm(response.data);
                $('#editBugModal').modal('show');
            } else {
                showErrorMessage('获取BUG信息失败: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取BUG信息失败:', error);
            showErrorMessage('获取BUG信息失败');
        }
    });
}

/**
 * 填充编辑表单
 */
function populateEditForm(bug) {
    $('#editBugId').val(bug.id);
    $('#editBugIdStr').val(bug.bug_id);
    $('#editBugType').val(bug.bug_type || '');
    $('#editSeverity').val(bug.severity || 'Medium');
    $('#editStatus').val(bug.status || 'Open');
    $('#editDescription').val(bug.description || '');
    $('#editSubmitSys').val(bug.submit_sys || '');
    $('#editVerificationStage').val(bug.verification_stage || '');
    $('#editDiscoveryPlatform').val(bug.discovery_platform || '');
    $('#editDiscoveryCase').val(bug.discovery_case || '');
    $('#editSubmitter').val(bug.submitter || '');
    $('#editVerifier').val(bug.verifier || '');
    $('#editSubmitDate').val(bug.submit_date || '');
    $('#editFixDate').val(bug.fix_date || '');
}

/**
 * 更新BUG
 */
function updateBug() {
    const form = $('#editBugForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const formData = new FormData(form);
    const bugData = {};
    const bugId = $('#editBugId').val();

    for (let [key, value] of formData.entries()) {
        if (key !== 'id') {
            bugData[key] = value;
        }
    }

    $.ajax({
        url: `/api/bugs/${bugId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(bugData),
        success: function(response) {
            if (response.success) {
                $('#editBugModal').modal('hide');
                showSuccessMessage('BUG更新成功');
                loadBugList();
                loadBugStatistics();
            } else {
                showErrorMessage('更新BUG失败: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('更新BUG失败:', error);
            showErrorMessage('更新BUG失败');
        }
    });
}

/**
 * 删除BUG
 */
function deleteBug(bugId, bugIdStr) {
    if (!confirm(`确定要删除BUG "${bugIdStr}" 吗？此操作不可恢复。`)) {
        return;
    }

    $.ajax({
        url: `/api/bugs/${bugId}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                showSuccessMessage('BUG删除成功');
                loadBugList();
                loadBugStatistics();
            } else {
                showErrorMessage('删除BUG失败: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('删除BUG失败:', error);
            showErrorMessage('删除BUG失败');
        }
    });
}

/**
 * 重置过滤器
 */
function resetFilters() {
    $('#statusFilter').val('');
    $('#severityFilter').val('');
    $('#searchInput').val('');
    currentPage = 1;
    loadBugList();
}

/**
 * 刷新BUG列表
 */
function refreshBugList() {
    loadBugList();
    loadBugStatistics();
    loadBugTrendData();
}

/**
 * 初始化图表
 */
function initializeCharts() {
    // 初始化状态分布图表
    const statusCtx = document.getElementById('bugStatusChart').getContext('2d');
    bugStatusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['未解决', '处理中', '已修复', '已关闭', '已拒绝'],
            datasets: [{
                data: [0, 0, 0, 0, 0],
                backgroundColor: [
                    '#dc3545', // 红色 - 未解决
                    '#ffc107', // 黄色 - 处理中
                    '#28a745', // 绿色 - 已修复
                    '#6c757d', // 灰色 - 已关闭
                    '#6f42c1'  // 紫色 - 已拒绝
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 初始化严重程度分布图表
    const severityCtx = document.getElementById('bugSeverityChart').getContext('2d');
    bugSeverityChart = new Chart(severityCtx, {
        type: 'pie',
        data: {
            labels: ['严重', '高', '中', '低'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    '#dc3545', // 红色 - 严重
                    '#fd7e14', // 橙色 - 高
                    '#ffc107', // 黄色 - 中
                    '#28a745'  // 绿色 - 低
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 初始化趋势图表
    const trendCtx = document.getElementById('bugTrendChart').getContext('2d');
    bugTrendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '新增BUG',
                data: [],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }, {
                label: '修复BUG',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1  // 确保Y轴显示整数
                    }
                }
            }
        }
    });

    // 加载趋势数据
    loadBugTrendData();
}

/**
 * 更新状态图表
 */
function updateStatusChart(statusData) {
    if (!bugStatusChart) return;

    const data = [
        statusData['Open'] || 0,
        statusData['In Progress'] || 0,
        statusData['Fixed'] || 0,
        statusData['Closed'] || 0,
        statusData['Rejected'] || 0
    ];

    bugStatusChart.data.datasets[0].data = data;
    bugStatusChart.update();
}

/**
 * 更新严重程度图表
 */
function updateSeverityChart(severityData) {
    if (!bugSeverityChart) return;

    const data = [
        severityData['Critical'] || 0,
        severityData['High'] || 0,
        severityData['Medium'] || 0,
        severityData['Low'] || 0
    ];

    bugSeverityChart.data.datasets[0].data = data;
    bugSeverityChart.update();
}

/**
 * 加载BUG趋势数据
 */
function loadBugTrendData(unit = 'day', chartType = 'line') {
    $.ajax({
        url: '/api/bugs/trend',
        method: 'GET',
        data: {
            days: unit === 'week' ? 28 : 30,  // 周视图显示4周，日视图显示30天
            unit: unit
        },
        success: function(response) {
            if (response.success) {
                updateTrendChart(response.data, chartType);
            } else {
                console.error('获取BUG趋势数据失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取BUG趋势数据失败:', error);
        }
    });
}

/**
 * 切换趋势图表类型
 */
function switchTrendChart(unit, chartType) {
    // 销毁现有图表
    if (bugTrendChart) {
        bugTrendChart.destroy();
    }

    // 重新创建图表
    const trendCtx = document.getElementById('bugTrendChart').getContext('2d');
    bugTrendChart = new Chart(trendCtx, {
        type: chartType,
        data: {
            labels: [],
            datasets: [{
                label: '新增BUG',
                data: [],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.6)',
                tension: chartType === 'line' ? 0.4 : 0
            }, {
                label: '修复BUG',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.6)',
                tension: chartType === 'line' ? 0.4 : 0
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1  // 确保Y轴显示整数
                    }
                }
            }
        }
    });

    // 加载新数据
    loadBugTrendData(unit, chartType);
}

/**
 * 更新趋势图表
 */
function updateTrendChart(trendData, chartType = 'line') {
    if (!bugTrendChart) return;

    // 格式化标签（如果是周数据，显示更友好的格式）
    let labels = trendData.labels || [];
    if (trendData.unit === 'week') {
        labels = labels.map(label => {
            // 将 "2024-W01" 格式转换为 "第1周"
            const weekMatch = label.match(/(\d{4})-W(\d{2})/);
            if (weekMatch) {
                return `第${parseInt(weekMatch[2])}周`;
            }
            return label;
        });
    }

    bugTrendChart.data.labels = labels;
    bugTrendChart.data.datasets[0].data = trendData.new_bugs || [];
    bugTrendChart.data.datasets[1].data = trendData.fixed_bugs || [];
    bugTrendChart.update();
}

// 工具函数

/**
 * 获取状态对应的颜色
 */
function getStatusColor(status) {
    const colors = {
        'Open': 'danger',
        'In Progress': 'warning',
        'Fixed': 'success',
        'Closed': 'secondary',
        'Rejected': 'dark'
    };
    return colors[status] || 'secondary';
}

/**
 * 获取严重程度对应的颜色
 */
function getSeverityColor(severity) {
    const colors = {
        'Critical': 'danger',
        'High': 'warning',
        'Medium': 'info',
        'Low': 'success'
    };
    return colors[severity] || 'secondary';
}

/**
 * 格式化日期
 */
function formatDate(dateStr) {
    if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN');
    } catch (e) {
        return dateStr;
    }
}

/**
 * HTML转义
 */
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    // 创建成功提示
    const alert = $(`
        <div class="alert alert-success alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `);

    $('body').append(alert);

    // 3秒后自动消失
    setTimeout(() => {
        alert.alert('close');
    }, 3000);
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    // 创建错误提示
    const alert = $(`
        <div class="alert alert-danger alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `);

    $('body').append(alert);

    // 5秒后自动消失
    setTimeout(() => {
        alert.alert('close');
    }, 5000);
}
